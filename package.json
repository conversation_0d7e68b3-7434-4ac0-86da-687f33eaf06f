{"name": "hazelsone", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fontsource/inter": "^5.1.1", "@tailwindcss/vite": "^4.0.6", "axios": "^1.8.1", "framer-motion": "^12.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-router-dom": "^7.2.0", "react-toastify": "^11.0.5", "typeface-inter": "^3.18.1"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.2", "tailwindcss": "^4.0.6", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}