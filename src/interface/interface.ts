export interface Story {
  id: number;
  text: string;
  name: string;
  avatar: string;
}
export interface Blog {
  id: number;
  title: string;
  description: string;
  content: string;
  authorName: string;
  authorImage: string;
  date: string;
  image: string;
  videos?: string[];
}

export interface ApiResponse<T> {
  status: boolean;
  message: string;
  data: T | null;
}

export interface QRCodeResponse {
  id: string;
  transactionId: string;
  status: string;
  creatorUserId: string;
  qrCodeKeyId: string;
  externalUserId: string;
}

export interface StaticQRDetailsResponse {
  firstName: string;
  lastName: string;
  companyName: string;
  currency: string;
  qrCodeName: string;
  defaultAmount?: number;
}

export interface StaticQRPaymentResponse {
  transactionId: string;
}

export interface CreateStaticQRPaymentNewData {
  userName: string;
  userEmail: string;
  staticQRId: string;
  currency: string;
  amount: number;
}
