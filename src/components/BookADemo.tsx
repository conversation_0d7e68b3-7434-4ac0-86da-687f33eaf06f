import { countries } from "../constants/constants";

function BookADemo() {
  return (
    <section className="min-h-[700px] w-full flex flex-col md:flex-row mb-10">
      <div
        className="relative flex-1 bg-cover bg-center flex items-center justify-center"
        style={{ backgroundImage: `url('/assets/demo-bg.jpg')` }}
      >
        <div className="absolute inset-0 bg-black/50"></div>

        <div className="relative z-10 p-8 md:p-12 text-white max-w-md flex flex-col items-center justify-center">
          <h2 className="text-3xl font-bold mb-4">
            Book a <span className="text-[#617C58]">demo</span>
          </h2>
          <p className="mb-2">
            Want to know more? Call us directly or let us contact you!
          </p>
        </div>
      </div>

      <div className="flex-1 bg-white p-8 md:p-12 flex items-center justify-center">
        <form className="w-full max-w-xl space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="companyName"
                className="block text-gray-700 text-sm font-medium mb-1"
              >
                Company name*
              </label>
              <input
                type="text"
                id="companyName"
                className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-400"
                required
              />
            </div>
            <div>
              <label
                htmlFor="orgNumber"
                className="block text-gray-700 text-sm font-medium mb-1"
              >
                Organization number
              </label>
              <input
                type="text"
                id="orgNumber"
                className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-400"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="fullName"
                className="block text-gray-700 text-sm font-medium mb-1"
              >
                Your name*
              </label>
              <input
                type="text"
                id="fullName"
                className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-400"
                required
              />
            </div>
            <div>
              <label
                htmlFor="email"
                className="block text-gray-700 text-sm font-medium mb-1"
              >
                Email address*
              </label>
              <input
                type="email"
                id="email"
                className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-400"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="phone"
                className="block text-gray-700 text-sm font-medium mb-1"
              >
                Your phone number
              </label>
              <input
                type="tel"
                id="phone"
                className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-400"
              />
            </div>
            <div>
              <label
                htmlFor="country"
                className="block text-gray-700 text-sm font-medium mb-1"
              >
                Country
              </label>
              <select
                id="country"
                className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-400"
              >
                <option value="">Select a country</option>
                {countries.map((country: string, index: number) => (
                  <option key={index} value={country}>
                    {country}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label
              htmlFor="message"
              className="block text-gray-700 text-sm font-medium mb-1"
            >
              Message
            </label>
            <textarea
              id="message"
              rows={4}
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-400"
            ></textarea>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="approve"
              className="h-4 w-4 text-orange-600 border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-orange-400"
              required
            />
            <label htmlFor="approve" className="text-sm text-gray-700">
              I agree that HazelsOne stores and processes my personal data as
              described in the privacy policy. I can withdraw my consent at any
              time.
            </label>
          </div>

          <a
            href="#contact"
            className="hidden md:inline-block bg-gray-900 text-white px-5 py-2 rounded-full text-sm font-medium hover:bg-gray-700 transition-colors"
          >
            Send
          </a>
        </form>
      </div>
    </section>
  );
}

export default BookADemo;
