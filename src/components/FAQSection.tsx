import Collapsible from "./Collapsible";

export const faqs = [
  {
    question:
      "Do I need to live in Europe to register as a HazelsOne Merchant?",
    answer:
      "Yes, HazelsOne currently supports merchants from the EU region and the United Kingdom.",
  },
  {
    question: "How long does it take to get approved?",
    answer:
      "Merchant approval typically takes 24-48 hours, depending on the verification process.",
  },
  {
    question: "What payment methods does HazelsOne support?",
    answer:
      "HazelsOne supports credit/debit cards, digital wallets, and QR-based payments.",
  },
  {
    question: "Is there a setup fee for using HazelsOne?",
    answer:
      "No, there are no setup fees. You only pay a transaction fee based on your plan.",
  },
  {
    question: "How secure is HazelsOne for processing payments?",
    answer:
      "HazelsOne employs advanced encryption protocols and complies with industry standards to ensure all transactions are secure. We use Secure Socket Layer (SSL) technology to encrypt data transmitted between merchants and our servers, safeguarding sensitive information.",
  },
  {
    question: "What devices are compatible with HazelsOne's payment solutions?",
    answer:
      "Our platform is compatible with Android devices running OS 8 and above, featuring NFC capabilities and an active internet connection.",
  },
  {
    question: "How can I integrate HazelsOne into my online store?",
    answer:
      "Once your application is approved, you'll receive a Merchant login ID. Our platform provides easy-to-follow instructions and code snippets for integrating HazelsOne into your website.",
  },
  {
    question: "What is 'Tap on Phone' and does HazelsOne support it?",
    answer:
      "'Tap on Phone' is a technology that enables merchants to accept contactless payments directly on their NFC-enabled Android devices without additional hardware. HazelsOne fully supports this feature.",
  },
  {
    question: "How can I contact HazelsOne support?",
    answer:
      "For assistance, contact us via <NAME_EMAIL> or submit a support ticket through our website. We’re available Monday to Friday, 9 AM - 5 PM CEST+1.",
  },
  {
    question: "What should I do if I encounter an error during a transaction?",
    answer:
      "Check the 'Transactions' section in your HazelsOne dashboard for status updates. If the issue persists, contact our support team for further assistance.",
  },
  {
    question: "Does HazelsOne provide transaction statements?",
    answer:
      "Yes, merchants can access 24/7 online transaction reporting through the HazelsOne dashboard.",
  },
  {
    question: "What is the payout schedule for merchants?",
    answer:
      "HazelsOne offers flexible payout options, including weekly and monthly payouts, based on merchant preferences.",
  },
];

const FAQSection = () => {
  return (
    <section className="w-full mt-20 py-16 px-6 md:px-12 lg:px-28 xl:px-36 text-center">
      {/* Heading */}
      <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
        FREQUENTLY ASKED QUESTIONS
      </h2>

      {/* FAQ List */}
      <div className="mt-10 max-w-4xl mx-auto space-y-4">
        {faqs.map((faq, index) => (
          <Collapsible
            key={index}
            question={faq.question}
            answer={faq.answer}
          />
        ))}
      </div>
    </section>
  );
};

export default FAQSection;
