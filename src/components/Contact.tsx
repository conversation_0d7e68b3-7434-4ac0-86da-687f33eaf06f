import React from "react";
import { motion } from "framer-motion";

const Contact: React.FC = () => {
  return (
    <section id="contact" className="py-24 bg-white">
      <div className="container text-center">
        <motion.h2
          className="text-3xl md:text-4xl font-bold mb-6"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          Ready to Transform Your Payment Experience?
        </motion.h2>
        <motion.p
          className="mb-8 text-lg md:text-xl text-gray-700"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
        >
          Join the revolution in payment processing with HazelsPay.
        </motion.p>
        <motion.a
          className="btn-primary"
          href="mailto:<EMAIL>"
          whileHover={{ scale: 1.05 }}
        >
          Contact Us Today
        </motion.a>
      </div>
    </section>
  );
};

export default Contact;
