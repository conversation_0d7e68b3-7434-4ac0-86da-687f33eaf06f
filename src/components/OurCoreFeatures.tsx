import React from "react";
import { AiOutlineCheck } from "react-icons/ai";
import { BiArrowFromLeft } from "react-icons/bi";
import { useNavigate } from "react-router-dom";

const OurCoreFeatures: React.FC = () => {
  const navigate = useNavigate();

  const handleNavigate = (path: string) => {
    navigate(path);
    window.scrollTo(0, 0);
  };

  return (
    <section className="py-20 px-4 bg-gradient-to-b from-white to-gray-50 md:mt-32">
      <div className="max-w-7xl mx-auto">
        <h2 className="text-3xl md:text-5xl font-bold text-center mb-20">
          Simple, Easy Payments, <br /> No Additional Hardware Required
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center mb-16">
          <div className="space-y-6 order-2 md:order-1">
            <h3 className="text-2xl font-bold text-gray-800">
              HazelPay – Standalone Terminal
            </h3>
            <p className="text-gray-700">
              HazelPay is a compact, mobile payment terminal designed for
              businesses on the go. Whether you’re operating a pop-up shop, food
              truck, or providing delivery services, HazelPay makes it
              effortless to accept payments anywhere, anytime.
            </p>
            <ul className="list-none space-y-3">
              <li className="flex items-start space-x-2">
                <AiOutlineCheck className="text-green-600 mt-1" />
                <span>
                  <strong>Mobility &amp; Versatility:</strong> Process payments
                  on the move, with built-in contactless and offline
                  capabilities.
                </span>
              </li>
              <li className="flex items-start space-x-2">
                <AiOutlineCheck className="text-green-600 mt-1" />
                <span>
                  <strong>Seamless Transactions:</strong> Lightning-fast, secure
                  transactions that support all major payment methods.
                </span>
              </li>
              <li className="flex items-start space-x-2">
                <AiOutlineCheck className="text-green-600 mt-1" />
                <span>
                  <strong>User-Friendly Design:</strong> Minimal training
                  required thanks to an intuitive interface and rugged build.
                </span>
              </li>
              <li className="flex items-start space-x-2">
                <AiOutlineCheck className="text-green-600 mt-1" />
                <span>
                  <strong>Robust Security:</strong> Advanced encryption ensures
                  every transaction is protected.
                </span>
              </li>
            </ul>
            <div
              onClick={() => handleNavigate("/hazelspay")}
              className=" text-black px-5 py-2 rounded flex flex-row items-center cursor-pointer hover:text-[#617C58]"
            >
              Learn More <BiArrowFromLeft className="w-5 h-7 ml-1" />
            </div>
          </div>

          <div className="order-1 md:order-2 flex justify-center">
            <img
              src="/assets/hazelsPay-terminal.png"
              alt="HazelPay Terminal"
              className="max-w-full h-auto rounded-lg shadow-lg"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div className="flex justify-center">
            <img
              src="/assets/hazelsPOS-system.jpeg"
              alt="HazelPOS System"
              className="max-w-full h-auto rounded-lg shadow-lg"
            />
          </div>

          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-gray-800">
              HazelPOS – Integrated Point of Sale
            </h3>
            <p className="text-gray-700">
              HazelPOS goes beyond payment processing to streamline your entire
              business operation. Manage sales, inventory, and customer data in
              one intuitive system, designed to scale with you as you grow.
            </p>
            <ul className="list-none space-y-3">
              <li className="flex items-start space-x-2">
                <AiOutlineCheck className="text-green-600 mt-1" />
                <span>
                  <strong>Complete Business Management:</strong> Track sales,
                  inventory, and employee schedules all from one dashboard.
                </span>
              </li>
              <li className="flex items-start space-x-2">
                <AiOutlineCheck className="text-green-600 mt-1" />
                <span>
                  <strong>Real-Time Analytics:</strong> Gain instant insights
                  into trends and customer behavior to drive growth.
                </span>
              </li>
              <li className="flex items-start space-x-2">
                <AiOutlineCheck className="text-green-600 mt-1" />
                <span>
                  <strong>Customizable &amp; Scalable:</strong> From small
                  boutiques to large enterprises, HazelPOS adapts to your needs.
                </span>
              </li>
              <li className="flex items-start space-x-2">
                <AiOutlineCheck className="text-green-600 mt-1" />
                <span>
                  <strong>Enhanced Customer Experience:</strong> Integrated
                  loyalty programs, digital receipts, and smooth checkouts.
                </span>
              </li>
            </ul>
            <div
              onClick={() => handleNavigate("/hazelspos")}
              className=" text-black px-5 py-2 rounded flex flex-row items-center cursor-pointer hover:text-[#617C58]"
            >
              Learn More <BiArrowFromLeft className="w-5 h-7 ml-1" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OurCoreFeatures;
