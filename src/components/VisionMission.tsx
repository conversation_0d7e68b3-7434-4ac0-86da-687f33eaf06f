import React from "react";

const VisionMission: React.FC = () => {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 space-y-16">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          <div>
            <img
              src="/assets/vision-bg.jpg"
              alt="Vision"
              className="w-full h-auto rounded-md shadow-lg"
            />
          </div>
          <div>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Vision</h2>
            <p className="text-gray-700 mb-4 leading-relaxed">
              At HazelsOne, we believe that modern payment solutions should be
              as dynamic as the businesses they serve. Our vision is to be the
              global standard for payment technology, delivering solutions that
              not only meet today’s demands but also anticipate tomorrow’s
              challenges.
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center bg-[#617C58]/20 rounded-xl p-8">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Mission</h2>
            <p className="text-gray-700 mb-4 leading-relaxed">
              At HazelsOne, our mission is to:
            </p>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>
                <strong>Simplify Transactions:</strong> Create a frictionless
                payment experience that makes every sale smooth and secure.
              </li>
              <li>
                <strong>Drive Growth:</strong> Empower businesses with real-time
                data and intuitive tools to unlock their full potential.
              </li>
              <li>
                <strong>Innovate Constantly:</strong> Stay ahead of the curve
                with technology that evolves with your needs.
              </li>
            </ul>
          </div>
          <div className="flex justify-center">
            <img
              src="/assets/mission-bg.jpg"
              alt="Mission"
              className="w-full object-cover rounded-md shadow-lg"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default VisionMission;
