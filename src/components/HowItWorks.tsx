import React from "react";

const HowItWorks: React.FC = () => {
  return (
    <section className="bg-white w-full">
      {/* Section Title */}
      <div className="text-center py-8">
        <h2 className="text-3xl md:text-4xl font-bold mb-4">How It Works</h2>
      </div>

      {/* Four-Column Grid */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-2 mx-3">
        {/* Step 1 */}
        <div className="relative flex flex-col items-center justify-center bg-white border p-6 text-center min-h-[35rem]">
          <h3 className="text-xl font-semibold mb-2">Get Started</h3>
          <p className="text-sm text-gray-600">
            Sign up for HazelPay and choose the solution that fits your business
            needs.
          </p>
          <div className="absolute bottom-3 right-3 text-gray-200 text-7xl font-bold pointer-events-none select-none">
            01
          </div>
        </div>

        {/* Step 2 (Background Image) */}
        <div
          className="relative flex flex-col items-center justify-center bg-center bg-cover bg-no-repeat text-white p-6 min-h-[35rem]"
          style={{
            backgroundImage: `url('/assets/howitworks-bg.jpg')`, // Replace with your image path
          }}
        >
          {/* Overlay */}
          <div className="absolute inset-0 bg-black/40"></div>

          {/* Text Content */}
          <div className="relative z-10 text-center">
            <h3 className="text-xl font-semibold mb-2">
              Setup &amp; Integration
            </h3>
            <p className="text-sm">
              Our easy onboarding process and seamless integration get you up
              and running in no time.
            </p>
          </div>

          {/* Background Number */}
          <div className="absolute bottom-3 right-3 text-white text-7xl font-bold pointer-events-none select-none opacity-60">
            02
          </div>
        </div>

        {/* Step 3 */}
        <div className="relative flex flex-col items-center justify-center bg-white border p-6 text-center min-h-[35rem]">
          <h3 className="text-xl font-semibold mb-2">Transact &amp; Grow</h3>
          <p className="text-sm text-gray-600">
            Use HazelPay or HazelPOS to process payments securely while
            accessing powerful tools to monitor and expand your business.
          </p>
          <div className="absolute bottom-3 right-3 text-gray-200 text-7xl font-bold pointer-events-none select-none">
            03
          </div>
        </div>

        {/* Step 4 */}
        <div className="relative flex flex-col items-center justify-center bg-white border p-6 text-center min-h-[35rem]">
          <h3 className="text-xl font-semibold mb-2">24/7 Support</h3>
          <p className="text-sm text-gray-600">
            Our dedicated support team is always available to assist, ensuring
            you have a smooth experience every step of the way.
          </p>
          <div className="absolute bottom-3 right-3 text-gray-200 text-7xl font-bold pointer-events-none select-none">
            04
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
