import { FC, useState } from "react";
import { clientStories } from "../constants/constants";

interface ClientStoriesProps {}

const ClientStories: FC<ClientStoriesProps> = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextStory = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % clientStories.length);
  };

  const prevStory = () => {
    setCurrentIndex(
      (prevIndex) =>
        (prevIndex - 1 + clientStories.length) % clientStories.length
    );
  };

  const { text, name, avatar } = clientStories[currentIndex];

  return (
    <section className="py-16 px-4 bg-white mt-20">
      <div className="text-center mb-12">
        <p className="text-sm text-gray-500 uppercase tracking-wide">
          Testimonials
        </p>
        <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mt-2">
          What People Say About Us
        </h2>
      </div>

      <div className="max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8 items-start">
        <div className="bg-gradient-to-br from-[#F0FAF9] to-white rounded-2xl p-8 shadow-md hover:shadow-lg transition-shadow duration-300">
          <h3 className="text-2xl font-semibold text-gray-900 mb-4">
            Work with us
          </h3>
          <p className="text-gray-700 text-lg leading-relaxed mb-6">{text}</p>
          <div className="flex items-center justify-center space-x-3 mb-8">
            <img
              src={avatar}
              alt={name}
              className="w-12 h-12 rounded-full object-cover shadow-sm"
            />
            <span className="font-semibold text-gray-800 text-lg">{name}</span>
          </div>

          <div className="flex items-center justify-center space-x-4">
            <button
              onClick={prevStory}
              className="w-12 h-12 rounded-full bg-white flex items-center justify-center shadow-md hover:bg-gray-100 text-teal-500 hover:text-teal-600 transition-all duration-300"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                strokeWidth="3"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <div className="flex items-center space-x-2">
              {clientStories.map((_, index) => (
                <div
                  key={index}
                  className={`w-3 h-3 rounded-full ${
                    index === currentIndex ? "bg-teal-500" : "bg-gray-300"
                  } hover:bg-teal-400 cursor-pointer transition-colors duration-300`}
                  onClick={() => setCurrentIndex(index)}
                />
              ))}
            </div>
            <button
              onClick={nextStory}
              className="w-12 h-12 rounded-full bg-white flex items-center justify-center shadow-md hover:bg-gray-100 text-teal-500 hover:text-teal-600 transition-all duration-300"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                strokeWidth="3"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        </div>

        <div className="hidden lg:block relative w-full h-[400px] items-center justify-center">
          <img
            src={avatar}
            alt={name}
            className="w-full h-full object-cover rounded-2xl shadow-md hover:shadow-lg transition-shadow duration-300"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent rounded-2xl"></div>
        </div>
      </div>
    </section>
  );
};

export default ClientStories;
