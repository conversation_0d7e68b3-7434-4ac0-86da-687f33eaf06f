import React from "react";

const WhyChooseHazelsOne: React.FC = () => {
  return (
    <section className="py-16 px-4">
      <div className="max-w-7xl mx-auto text-center mb-10">
        <h2 className="text-3xl md:text-4xl font-bold mb-4">
          Why Choose HazelsOne?
        </h2>
        <p className="text-gray-600">
          Empower Your Business with Innovation and Reliability
        </p>
      </div>

      <div className="max-w-7xl mx-auto bg-[#617C58]/20 rounded-2xl p-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="flex flex-col items-center text-center">
            <div className="mb-4 text-blue-500">
              <svg
                className="w-10 h-10"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            </div>
            {/* Title */}
            <h3 className="text-lg font-semibold mb-2">
              Speed &amp; Efficiency
            </h3>
            {/* Text */}
            <p className="text-sm text-gray-700">
              Experience rapid transactions that reduce wait times and boost
              customer satisfaction.
            </p>
          </div>

          {/* 2. Enhanced Security */}
          <div className="flex flex-col items-center text-center">
            <div className="mb-4 text-blue-500">
              <svg
                className="w-10 h-10"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 11c.338 0 .67.028 1 .082V6a4 4 0
                   10-8 0v5.082c.33-.054.662-.082 1-.082a3
                   3 0 110 6 3 3 0 110-6z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">Enhanced Security</h3>
            <p className="text-sm text-gray-700">
              Our state-of-the-art security measures ensure every transaction is
              protected against fraud.
            </p>
          </div>

          {/* 3. Scalable Solutions */}
          <div className="flex flex-col items-center text-center">
            <div className="mb-4 text-blue-500">
              <svg
                className="w-10 h-10"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M9 20l-5.447-2.724A2 2 0
                   013 15.382V8.618a2 2 0
                   01.553-1.382L9 4m6 16l5.447-2.724A2 2 0
                   0021 15.382V8.618a2 2 0
                   00-.553-1.382L15 4M3 8l9 6 9-6"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">Scalable Solutions</h3>
            <p className="text-sm text-gray-700">
              From small shops to large enterprises, our products are designed
              to grow with your business.
            </p>
          </div>

          {/* 4. Expert Support */}
          <div className="flex flex-col items-center text-center">
            <div className="mb-4 text-blue-500">
              <svg
                className="w-10 h-10"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M9.75 17a1.5 1.5 0
                   100 3 1.5 1.5 0 000-3zm4.5 0a1.5 1.5
                   0 100 3 1.5 1.5 0 000-3zM6.503
                   4.871l1.33 2.662m9.334
                   0l1.33-2.662M6.75 7.533h10.5M6.75
                   7.533L5.17 10.29a2 2 0 00-.17.807v4.076a2
                   2 0 002 2h10a2 2 0 002-2v-4.076a2 2 0
                   00-.17-.807l-1.58-2.757"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">Expert Support</h3>
            <p className="text-sm text-gray-700">
              With 24/7 customer support and continuous system updates, we’re
              always here to help you succeed.
            </p>
          </div>

          {/* 5. Contactless & SoftPOS */}
          <div className="flex flex-col items-center text-center">
            <div className="mb-4 text-blue-500">
              <svg
                className="w-10 h-10"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M2 9l3 3-3 3M22
                   9l-3 3 3 3M6.343 6.343l2.829
                   2.828-2.829 2.829M17.657
                   6.343l-2.829 2.828 2.829
                   2.829"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">
              Contactless &amp; SoftPOS
            </h3>
            <p className="text-sm text-gray-700">
              Accept touch-free payments and leverage SoftPOS technology for
              seamless, modern transactions.
            </p>
          </div>

          {/* 6. Real-Time Analytics */}
          <div className="flex flex-col items-center text-center">
            <div className="mb-4 text-blue-500">
              <svg
                className="w-10 h-10"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M11 11V3H5a2 2 0
                   00-2 2v14a2 2 0 002 2h14a2 2 0
                   002-2v-6h-8a2 2 0
                   01-2-2z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">Real-Time Analytics</h3>
            <p className="text-sm text-gray-700">
              Gain instant insights into sales, trends, and customer behaviors
              for smarter business decisions.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseHazelsOne;
