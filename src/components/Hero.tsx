import { FC } from "react";

interface HeroProps {}

const Hero: FC<HeroProps> = () => {
  return (
    <section className="relative w-full min-h-[80vh] flex flex-col md:flex-row items-center justify-center overflow-hidden">
      <div className="w-full md:w-1/2 h-[50vh] md:h-[90vh] flex items-center justify-center overflow-hidden md:order-2">
        <div className="absolute inset-0 z-0 bg-[url('/assets/herobg1.jpg')] bg-cover bg-center opacity-10"></div>
        <div className="relative z-10 w-full max-w-5xl px-4 md:px-12 lg:px-16">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 items-center justify-center">
            <div className="relative w-full h-[150px] md:h-[200px] animate-float-slow">
              <img
                src="/assets/hero1.jpg"
                alt="Person using smartphone for payment"
                className="w-full h-full object-cover rounded-lg shadow-md"
              />
            </div>

            <div className="relative w-full h-[150px] md:h-[200px] animate-float">
              <img
                src="/assets/hero4.jpg"
                alt="Lady holding a smartphone for payment"
                className="w-full h-full object-cover rounded-lg shadow-md"
              />
            </div>

            <div className="relative w-full h-[250px] md:h-[400px] col-span-1 md:col-span-3 flex items-center justify-center animate-pulse-slow">
              <video
                src="/assets/tapvid.mp4"
                autoPlay
                loop
                muted
                playsInline
                className="w-full h-full object-cover rounded-lg shadow-md"
              />
            </div>

            <div className="relative w-full h-[150px] md:h-[200px] animate-float-slow">
              <img
                src="/assets/hero2.jpg"
                alt="Man using smartphone for payment in coffee shop"
                className="w-full h-full object-cover rounded-lg shadow-md"
              />
            </div>

            <div className="relative w-full h-[150px] md:h-[200px] animate-float">
              <img
                src="/assets/hero3.jpg"
                alt="Woman using smartphone for payment in office"
                className="w-full h-full object-cover rounded-lg shadow-md"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="w-full md:w-1/2 h-auto md:h-[90vh] flex items-center justify-center bg-gray-200 p-6 md:p-12 lg:p-16 z-20 order-1 md:order-1">
        <div className="max-w-2xl text-black text-center md:text-left">
          <h1 className="text-3xl md:text-5xl lg:text-6xl font-extrabold leading-tight mb-8 md:mb-6 lg:mb-4">
            Power Your <span className="text-[#617C58]">Payments</span> with
            HazelsOne
          </h1>
          <p className="text-base md:text-lg lg:text-xl mb-8 md:mb-6 lg:mb-4 text-gray-700">
            Revolutionizing transactions, one tap at a time.
          </p>
          <a
            href="/company"
            className="inline-flex items-center px-5 md:px-6 py-3 md:py-3 text-base md:text-lg lg:text-xl font-medium bg-[#617C58] text-black rounded-full hover:bg-[#FFFFFF] hover:shadow-lg transition-all duration-300 transform hover:scale-105"
          >
            <svg
              className="w-5 h-5 md:w-6 md:h-6 mr-2 group-hover:translate-x-1 transition-transform"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M10.293 15.707a1 1 0 010-1.414L13.586 11H3a1 1 0 110-2h10.586l-3.293-3.293a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z"
                clipRule="evenodd"
              />
            </svg>
            Learn more
          </a>
        </div>
      </div>
    </section>
  );
};

export default Hero;
