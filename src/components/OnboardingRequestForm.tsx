import React, { useState } from "react";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { Link } from "react-router-dom";
import { countries } from "../constants/constants";
import { validateEmail } from "../utils/validators";
import { submitOnboardingRequest } from "../services/onboardingService";

const OnboardingRequestForm: React.FC = () => {
  const [email, setEmail] = useState("");
  const [merchantType, setMerchantType] = useState("Select");
  const [country, setCountry] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [expectedTransactions, setExpectedTransactions] = useState("Select");
  const [expectedActiveTerminals, setExpectedActiveTerminals] =
    useState("Select");
  const [acceptedTerms, setAcceptedTerms] = useState(false);

  const resetForm = () => {
    setEmail("");
    setMerchantType("Select");
    setCountry("");
    setCompanyName("");
    setPhoneNumber("");
    setExpectedTransactions("Select");
    setExpectedActiveTerminals("Select");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateEmail(email)) {
      toast.error("Please enter a valid email address");
      return;
    }

    if (merchantType === "Select") {
      toast.error("Please select a merchant type");
      return;
    }

    if (!companyName) {
      toast.error("Company Name is required");
      return;
    }

    if (!country) {
      toast.error("Please select a country");
      return;
    }

    if (expectedTransactions === "Select") {
      toast.error("Please select the expected transaction volume");
      return;
    }

    if (expectedActiveTerminals === "Select") {
      toast.error("Please select the expected number of active terminals");
      return;
    }

    const payload = {
      email,
      merchantType,
      country,
      companyName,
      phone: phoneNumber,
      expectedTransactionsPerMonth: expectedTransactions,
      expectedMonthlyActiveTerminals: expectedActiveTerminals,
    };

    try {
      await submitOnboardingRequest(payload)
        .then((res) => {
          toast.dismiss();
          toast.success(res.message);
          resetForm();
        })
        .catch((error) => {
          toast.dismiss();
          toast.error(error || "An error occurred");
        });
    } catch (error) {
      toast.error("Unexpected error occurred. Please try again.");
    }
  };

  return (
    <div className="flex flex-col pt-32 pb-32 items-center justify-center bg-white">
      <div className="relative w-full max-w-lg bg-white p-8 rounded-lg shadow-2xl">
        <div
          className="flex items-center text-primary text-sm cursor-pointer"
          onClick={() => window.open("/", "_self")}
        >
          <span>&larr; Back to Homepage</span>
        </div>

        <div className="flex p-10 items-center justify-center">
          <img
            src="/assets/hazelsonelogo.png"
            alt="HazelsOne Logo"
            className="w-8 h-8"
          />
          <span className="text-2xl font-bold ml-2">
            Hazels<span className="text-[#617C58]">One</span>
          </span>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label
              htmlFor="email"
              className="block text-sm font-medium text-primary"
            >
              Email Address <span className="text-red-500">*</span>
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Enter your email"
              required
            />
          </div>

          <div className="mb-4">
            <label
              htmlFor="merchant-type"
              className="block text-sm font-medium text-primary"
            >
              Merchant Type <span className="text-red-500">*</span>
            </label>
            <select
              id="merchant-type"
              value={merchantType}
              onChange={(e) => setMerchantType(e.target.value)}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="Select">Select</option>
              <option value="ONLINE">Online</option>
              <option value="CARDPRESENT">In-Store</option>
              <option value="GROUP">Online/In-Store</option>
            </select>
          </div>

          <div className="mb-4">
            <label
              htmlFor="country"
              className="block text-sm font-medium text-primary"
            >
              Country <span className="text-red-500">*</span>
            </label>

            <select
              id="country"
              value={country}
              onChange={(e) => setCountry(e.target.value)}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary max-h-[200px] overflow-y-auto"
              required
            >
              <option value="">Select a country</option>
              {countries.map((country: string, index: number) => (
                <option key={index} value={country}>
                  {country}
                </option>
              ))}
            </select>
          </div>

          <div className="mb-4">
            <label
              htmlFor="company-name"
              className="block text-sm font-medium text-primary"
            >
              Company Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="company-name"
              value={companyName}
              onChange={(e) => setCompanyName(e.target.value)}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Enter your company name"
              required
            />
          </div>

          <div className="mb-4">
            <label
              htmlFor="expected-transactions"
              className="block text-sm font-medium text-primary"
            >
              Expected Volume of Transactions (€ Monthly){" "}
              <span className="text-red-500">*</span>
            </label>
            <select
              id="expected-transactions"
              value={expectedTransactions}
              onChange={(e) => setExpectedTransactions(e.target.value)}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="Select">Select</option>
              <option value="Less than 100000">Less than 100,000</option>
              <option value="Up to 500,000">Up to 500,000</option>
              <option value="Up to 1,000,000">Up to 1,000,000</option>
              <option value="More than 1,000,000">More than 1,00,0000</option>
            </select>
          </div>

          <div className="mb-4">
            <label
              htmlFor="expected-active-terminals"
              className="block text-sm font-medium text-primary flex items-center justify-between"
            >
              <span>
                Expected Active Terminals (Monthly){" "}
                <span className="text-red-500">*</span>
              </span>
              <span
                className="ml-1 text-gray-500 cursor-pointer"
                title="A payment terminal is a device that can process transactions and is used to do payments."
                style={{
                  position: "relative",
                  display: "inline-block",
                }}
              >
                ℹ️
              </span>
            </label>
            <select
              id="expected-active-terminals"
              value={expectedActiveTerminals}
              onChange={(e) => setExpectedActiveTerminals(e.target.value)}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="Select">Select</option>
              <option value="Less than 100">Less than 100</option>
              <option value="Up to 500">Upto 500</option>
              <option value="Up to 1000">Upto 1000</option>
              <option value="More than 1000">More than 1000</option>
            </select>
          </div>

          <div className="mb-4">
            <label
              htmlFor="phone-number"
              className="block text-sm font-medium text-primary"
            >
              Phone Number
            </label>
            <input
              type="tel"
              id="phone-number"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Enter your phone number"
            />
          </div>

          <div className="flex items-center mt-6 mb-6">
            <input
              type="checkbox"
              id="terms"
              checked={acceptedTerms}
              onChange={(e) => setAcceptedTerms(e.target.checked)}
              className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-2 focus:ring-primary"
            />
            <label htmlFor="terms" className="ml-2 text-sm text-gray-600">
              I agree to the{" "}
              <Link to="/termsofuse" className="text-blue-600 hover:underline">
                Terms of Use
              </Link>
            </label>
          </div>

          <button
            type="submit"
            className={`w-full py-2 rounded-md text-white font-semibold transition ${
              acceptedTerms
                ? "bg-[#617C58] cursor-pointer"
                : "bg-gray-400 cursor-not-allowed"
            }`}
            disabled={!acceptedTerms}
          >
            Submit Request
          </button>
        </form>
      </div>

      <ToastContainer />
    </div>
  );
};

export default OnboardingRequestForm;
