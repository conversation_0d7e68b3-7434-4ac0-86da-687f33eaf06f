import { useState } from "react";

interface CollapsibleProps {
  question: string;
  answer: string;
}

const Collapsible = ({ question, answer }: CollapsibleProps) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-md">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex justify-between items-center p-4 text-left text-gray-800 font-medium"
      >
        <span>{question}</span>
        <span className="text-xl">{isOpen ? "−" : "+"}</span>
      </button>

      {isOpen && (
        <div className="p-4 border-t border-gray-200 text-gray-600 text-md text-left">
          {answer}
        </div>
      )}
    </div>
  );
};

export default Collapsible;
