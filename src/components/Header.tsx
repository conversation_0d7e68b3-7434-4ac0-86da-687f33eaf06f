import { FC, useState } from "react";
import { useNavigate } from "react-router-dom";

interface HeaderProps {}

interface DropdownItem {
  label: string;
  href: string;
  icon: React.ReactNode;
  external?: boolean;
}

const Header: FC<HeaderProps> = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null);
  const navigate = useNavigate();

  const serverIcon = "/assets/servericon.svg";

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const openDropdown = (key: string) => {
    setDropdownOpen(key);
  };

  const closeDropdown = () => {
    setDropdownOpen(null);
  };

  const dropdownContent: Record<string, DropdownItem[]> = {
    solutions: [
      {
        label: "HazelPay",
        href: "/hazelpay",
        icon: (
          <svg
            className="w-5 h-5 mr-2 text-gray-600"
            width="24px"
            height="24px"
            viewBox="0 0 32 32"
            enable-background="new 0 0 32 32"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            fill="#000000"
          >
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g
              id="SVGRepo_tracerCarrier"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></g>
            <g id="SVGRepo_iconCarrier">
              <g id="Layer_1"></g>
              <g id="Layer_2">
                <g>
                  <polyline
                    fill="none"
                    points=" 2,10 2,6 6,6 "
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-miterlimit="10"
                    stroke-width="2"
                  ></polyline>
                  <polyline
                    fill="none"
                    points=" 30,10 30,6 26,6 "
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-miterlimit="10"
                    stroke-width="2"
                  ></polyline>
                  <polyline
                    fill="none"
                    points=" 2,22 2,26 6,26 "
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-miterlimit="10"
                    stroke-width="2"
                  ></polyline>
                  <polyline
                    fill="none"
                    points=" 30,22 30,26 26,26 "
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-miterlimit="10"
                    stroke-width="2"
                  ></polyline>
                  <line
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-miterlimit="10"
                    stroke-width="2"
                    x1="6"
                    x2="6"
                    y1="9"
                    y2="15"
                  ></line>
                  <line
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-miterlimit="10"
                    stroke-width="2"
                    x1="11"
                    x2="11"
                    y1="9"
                    y2="15"
                  ></line>
                  <line
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-miterlimit="10"
                    stroke-width="2"
                    x1="26"
                    x2="26"
                    y1="9"
                    y2="15"
                  ></line>
                  <line
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-miterlimit="10"
                    stroke-width="2"
                    x1="21"
                    x2="21"
                    y1="9"
                    y2="15"
                  ></line>
                  <line
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-miterlimit="10"
                    stroke-width="2"
                    x1="16"
                    x2="16"
                    y1="9"
                    y2="15"
                  ></line>
                  <line
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-miterlimit="10"
                    stroke-width="2"
                    x1="2"
                    x2="30"
                    y1="18"
                    y2="18"
                  ></line>
                  <line
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-miterlimit="10"
                    stroke-width="2"
                    x1="6"
                    x2="6"
                    y1="21"
                    y2="23"
                  ></line>
                  <line
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-miterlimit="10"
                    stroke-width="2"
                    x1="11"
                    x2="11"
                    y1="21"
                    y2="22"
                  ></line>
                  <line
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-miterlimit="10"
                    stroke-width="2"
                    x1="26"
                    x2="26"
                    y1="21"
                    y2="23"
                  ></line>
                  <line
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-miterlimit="10"
                    stroke-width="2"
                    x1="21"
                    x2="21"
                    y1="21"
                    y2="22"
                  ></line>
                  <line
                    fill="none"
                    stroke="#000000"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-miterlimit="10"
                    stroke-width="2"
                    x1="16"
                    x2="16"
                    y1="21"
                    y2="22"
                  ></line>
                </g>
              </g>
            </g>
          </svg>
        ),
      },
      {
        label: "HazelPOS",
        href: "/hazelpos",
        icon: (
          <svg
            className="w-5 h-5 mr-2 text-gray-600"
            fill="#000000"
            height="24px"
            width="24px"
            version="1.1"
            id="Capa_1"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 493.662 493.662"
          >
            <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
            <g
              id="SVGRepo_tracerCarrier"
              strokeLinecap="round"
              strokeLinejoin="round"
            ></g>
            <g id="SVGRepo_iconCarrier">
              <g>
                <path d="M329.587,114h-19.756V10.5c0-5.799-4.701-10.5-10.5-10.5h-105c-5.799,0-10.5,4.701-10.5,10.5V114h-19.756 c-17.561,0-31.85,14.287-31.85,31.849v315.964c0,17.561,14.288,31.849,31.85,31.849h165.512c17.561,0,31.849-14.287,31.849-31.849 V145.85C361.436,128.288,347.149,114,329.587,114z M298.806,175.974v70.39H194.857v-70.39H298.806z M288.831,21v133.564l-84-0.001 V21H288.831z M340.436,461.813c0,5.982-4.867,10.849-10.849,10.849H164.075c-5.982,0-10.85-4.867-10.85-10.849V145.849 c0-5.982,4.867-10.849,10.85-10.849h19.756v19.589c-2.593,0.13-5.055,1.205-6.899,3.049c-1.969,1.969-3.075,4.64-3.075,7.425v91.8 c0,5.799,4.701,10.5,10.5,10.5h124.949c5.799,0,10.5-4.701,10.5-10.5v-91.8c0-5.622-4.422-10.198-9.975-10.474V135h19.756 c5.982,0,10.849,4.867,10.849,10.85V461.813z"></path>
                <path d="M231.148,299.735h-47.05c-5.799,0-10.5,4.701-10.5,10.5c0,5.799,4.701,10.5,10.5,10.5h47.05c5.799,0,10.5-4.701,10.5-10.5 C241.648,304.436,236.947,299.735,231.148,299.735z"></path>
                <path d="M309.564,299.735h-47.049c-5.799,0-10.5,4.701-10.5,10.5c0,5.799,4.701,10.5,10.5,10.5h47.049 c5.799,0,10.5-4.701,10.5-10.5C320.064,304.436,315.362,299.735,309.564,299.735z"></path>
                <path d="M231.148,354.175h-47.05c-5.799,0-10.5,4.701-10.5,10.5s4.701,10.5,10.5,10.5h47.05c5.799,0,10.5-4.701,10.5-10.5 S236.947,354.175,231.148,354.175z"></path>
                <path d="M309.564,354.175h-47.049c-5.799,0-10.5,4.701-10.5,10.5s4.701,10.5,10.5,10.5h47.049c5.799,0,10.5-4.701,10.5-10.5 S315.362,354.175,309.564,354.175z"></path>
                <path d="M231.148,409.683h-47.05c-5.799,0-10.5,4.701-10.5,10.5c0,5.799,4.701,10.5,10.5,10.5h47.05c5.799,0,10.5-4.701,10.5-10.5 C241.648,414.384,236.947,409.683,231.148,409.683z"></path>
                <path d="M309.564,409.683h-47.049c-5.799,0-10.5,4.701-10.5,10.5c0,5.799,4.701,10.5,10.5,10.5h47.049 c5.799,0,10.5-4.701,10.5-10.5C320.064,414.384,315.362,409.683,309.564,409.683z"></path>
              </g>
            </g>
          </svg>
        ),
      },
    ],
    company: [
      {
        label: "About Us",
        href: "/company",
        icon: (
          <svg
            className="w-5 h-5 mr-2 text-gray-600"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
            />
          </svg>
        ),
      },
      // {
      //   label: "Our Team",
      //   href: "/team",
      //   icon: (
      //     <svg
      //       className="w-5 h-5 mr-2 text-gray-600"
      //       fill="none"
      //       stroke="currentColor"
      //       strokeWidth="2"
      //       viewBox="0 0 24 24"
      //     >
      //       <path
      //         strokeLinecap="round"
      //         strokeLinejoin="round"
      //         d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0z"
      //       />
      //     </svg>
      //   ),
      // },
    ],
    support: [
      {
        label: "Help Center",
        href: "/help",
        icon: (
          <svg
            className="w-5 h-5 mr-2 text-gray-600"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        ),
      },
      {
        label: "Server Status",
        href: "https://status.hazelsone.com/",
        external: true,
        icon: (
          <img
            src={serverIcon}
            alt="Server Status Icon"
            className="w-5 h-5 mr-2"
          />
        ),
      },
    ],
  };

  const navItems = [
    { key: "home", label: "Home", path: "/" },
    {
      key: "solutions",
      label: "Solutions",
      dropdown: dropdownContent.solutions,
    },
    { key: "company", label: "Company", dropdown: dropdownContent.company },
    { key: "support", label: "Support", dropdown: dropdownContent.support },
  ];

  return (
    <header className="w-full font-sans text-gray-800 z-50">
      <div className="border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between relative">
          {/* Logo */}
          <div
            onClick={() => navigate("/")}
            className="flex items-center space-x-2 cursor-pointer"
          >
            <img
              src="/assets/hazelsonelogo.png"
              alt="HazelsOne Logo"
              className="w-8 h-8"
            />
            <span className="text-2xl font-bold">
              Hazels<span className="text-[#617C58]">One</span>
            </span>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6 text-sm font-medium">
            {navItems.map((item) => (
              <div key={item.key} className="relative group">
                {item.path ? (
                  <button
                    onClick={() => navigate(item.path)}
                    className="cursor-pointer hover:text-green-800 transition py-3 px-4 rounded-md"
                  >
                    {item.label}
                  </button>
                ) : (
                  <div
                    className="relative"
                    onMouseEnter={() => openDropdown(item.key)}
                  >
                    <button className="cursor-pointer hover:text-green-800 transition py-3 px-4 rounded-md">
                      {item.label}
                    </button>

                    {/* Dropdown menu */}
                    {dropdownOpen === item.key && (
                      <div className="dropdown-container">
                        {/* Invisible bridge to prevent mouse leaving */}
                        <div
                          className="absolute top-full left-0 w-full h-5"
                          onMouseEnter={() => openDropdown(item.key)}
                        ></div>

                        {/* Actual dropdown */}
                        <div
                          className="absolute top-[calc(100%+5px)] left-0 bg-white shadow-lg rounded-lg py-3 w-60 border border-gray-200 z-[1000]"
                          onMouseEnter={() => openDropdown(item.key)}
                          onMouseLeave={closeDropdown}
                        >
                          <div className="px-4 py-2 text-gray-900 font-semibold">
                            {item.label} Options
                          </div>

                          {item.dropdown?.map((subItem) =>
                            subItem.external ? (
                              <a
                                key={subItem.href}
                                href={subItem.href}
                                target="_blank"
                                className="flex items-center px-6 py-2 hover:bg-gray-100"
                              >
                                {subItem.icon}
                                {subItem.label}
                              </a>
                            ) : (
                              <button
                                key={subItem.href}
                                onClick={() => navigate(subItem.href)}
                                className="flex items-center w-full px-6 py-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
                              >
                                {subItem.icon}
                                {subItem.label}
                              </button>
                            )
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </nav>

          <button
            onClick={() => navigate("/joinus")}
            className="cursor-pointer hidden md:inline-block bg-gray-900 text-white px-5 py-2 rounded-full text-sm font-medium hover:bg-gray-700 transition-colors"
          >
            Let's Talk
          </button>

          <button
            className="md:hidden focus:outline-none"
            onClick={toggleMobileMenu}
          >
            <svg
              className="w-6 h-6 text-gray-800"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </button>

          {/* Mobile Navigation */}
          {isMobileMenuOpen && (
            <div className="absolute top-16 right-0 w-full bg-white shadow-lg md:hidden z-[1000]">
              <nav className="flex flex-col items-start space-y-4 p-4 text-sm font-medium">
                {navItems.map((item) => (
                  <div key={item.key} className="w-full">
                    {item.dropdown ? (
                      <>
                        <button
                          onClick={() =>
                            setHoveredItem(
                              item.key === hoveredItem ? null : item.key
                            )
                          }
                          className="cursor-pointer w-full text-left py-3 px-4 hover:text-green-800 transition-colors"
                        >
                          {item.label}
                        </button>
                        {hoveredItem === item.key && (
                          <div className="pl-4 mt-2 w-full bg-gray-50 rounded-md border border-gray-200 z-[1000] cursor-pointer">
                            {item.dropdown?.map((subItem) =>
                              subItem.external ? (
                                <a
                                  key={subItem.href}
                                  href={subItem.href}
                                  target="_blank"
                                  className="cursor-pointer flex items-center w-full px-5 py-3 text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
                                  onClick={() => setIsMobileMenuOpen(false)}
                                >
                                  {subItem.icon}
                                  {subItem.label}
                                </a>
                              ) : (
                                <button
                                  key={subItem.href}
                                  onClick={() => {
                                    navigate(subItem.href);
                                    setIsMobileMenuOpen(false);
                                  }}
                                  className="cursor-pointer flex items-center w-full px-5 py-3 text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
                                >
                                  {subItem.icon}
                                  {subItem.label}
                                </button>
                              )
                            )}
                          </div>
                        )}
                      </>
                    ) : (
                      <button
                        onClick={() => {
                          navigate(item.path!);
                          setIsMobileMenuOpen(false);
                        }}
                        className="cursor-pointer w-full text-left py-3 px-4 hover:text-green-800 transition-colors"
                      >
                        {item.label}
                      </button>
                    )}
                  </div>
                ))}
              </nav>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
