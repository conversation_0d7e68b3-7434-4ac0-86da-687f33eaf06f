import { HiOutlineLightBulb } from "react-icons/hi"; // Example Icon

const ComingSoon = () => {
  return (
    <div className="flex flex-col items-center justify-center h-[85vh] text-center bg-gray-100 mb-[-80px]">
      <HiOutlineLightBulb className="text-[#617C58] text-6xl mb-4 animate-pulse" />

      <h1 className="text-2xl md:text-4xl font-semibold text-gray-900">
        🚀 Something Exciting is Coming!
      </h1>

      <p className="mt-4 text-lg md:text-xl text-gray-600 max-w-lg">
        We're working hard to bring you an{" "}
        <strong className="text-primary">amazing experience</strong>. Stay tuned
        while we finalize this page!
      </p>

      <a
        href="/"
        className="mt-6 bg-[#617C58] text-white px-6 py-3 rounded-lg shadow-md hover:bg-white hover:text-black transition"
      >
        Go Back Home
      </a>
    </div>
  );
};

export default ComingSoon;
