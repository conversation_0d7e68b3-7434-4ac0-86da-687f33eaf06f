import React, { useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  HiCredit<PERSON>ard,
  HiCurrencyDollar,
  Hi<PERSON>ser,
  HiOfficeBuilding,
  HiTag,
} from "react-icons/hi";
import { StaticQRDetailsResponse } from "../interface/interface";
import {
  fetchStaticQRDetails,
  createStaticQRPaymentNew,
  getCheckoutUrl,
} from "../services/paymentService";

const StaticQrPayment: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const staticQRId = searchParams.get("id");

  const [qrDetails, setQrDetails] = useState<StaticQRDetailsResponse | null>(
    null
  );
  const [amount, setAmount] = useState<string>("");
  const [userName, setUserName] = useState<string>("");
  const [userEmail, setUserEmail] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [appRedirected, setAppRedirected] = useState<boolean>(false);

  useEffect(() => {
    if (staticQRId) {
      const appLink = `https://hazelpay.com/preprod/pay/static-qr?id=${staticQRId}`;
      window.location.href = appLink;
      setAppRedirected(true);

      const timer = setTimeout(() => {
        fetchQRDetails();
      }, 1500);

      return () => clearTimeout(timer);
    } else {
      setError("Invalid QR code");
      setIsLoading(false);
    }
  }, [staticQRId]);

  const fetchQRDetails = async () => {
    if (!staticQRId) {
      setError("Invalid QR code");
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetchStaticQRDetails(staticQRId);

      if (response.status && response.data) {
        setQrDetails(response.data);
        if (response.data.defaultAmount) {
          setAmount(response.data.defaultAmount.toString());
        }
      } else {
        setError(response.message || "Failed to fetch QR details");
      }
    } catch (err: any) {
      setError(err.message || "An error occurred while fetching QR details");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (/^(\d+)?(\.\d{0,2})?$/.test(value) || value === "") {
      setAmount(value);
    }
  };

  const handleUserNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUserName(e.target.value);
  };

  const handleUserEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUserEmail(e.target.value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!staticQRId || !qrDetails) {
      toast.error("Invalid QR code or missing details");
      return;
    }

    if (!amount || parseFloat(amount) <= 0) {
      toast.error("Please enter a valid amount");
      return;
    }

    if (!userName.trim()) {
      toast.error("Please enter your name");
      return;
    }

    if (!userEmail.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userEmail)) {
      toast.error("Please enter a valid email address");
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      const paymentData = {
        userName: userName,
        userEmail: userEmail,
        staticQRId: staticQRId,
        currency: qrDetails.currency,
        amount: parseFloat(amount),
      };

      const response = await createStaticQRPaymentNew(paymentData);

      if (response.status && response.data) {
        const transactionId = response.data;
        window.location.href = getCheckoutUrl(transactionId);
      } else {
        toast.error(response.message || "Failed to create payment");
        setIsSubmitting(false);
      }
    } catch (err: any) {
      toast.error(err.message || "An error occurred while processing payment");
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-md px-6 py-10 bg-white rounded-lg shadow-md text-center"
        >
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            {appRedirected
              ? "Opening HazelPay..."
              : "Loading payment details..."}
          </h1>
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#617C58] mb-4"></div>
            <p className="text-gray-600">
              {appRedirected
                ? "If the app doesn't open, we'll process your payment in the browser."
                : "Loading payment details..."}
            </p>
          </div>
        </motion.div>
      </div>
    );
  }

  if (error || !qrDetails) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-md w-full bg-white rounded-lg shadow-xl overflow-hidden"
        >
          <div className="bg-red-50 border-b border-red-200 p-8 flex flex-col items-center justify-center text-center">
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <svg
                className="w-16 h-16 text-red-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
            </motion.div>
            <motion.h1
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="text-2xl font-bold mt-4 text-gray-800"
            >
              Error Loading Payment
            </motion.h1>
          </div>

          <div className="p-8">
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="text-lg text-gray-600 mb-8 text-center"
            >
              {error || "Unable to load payment details. Please try again."}
            </motion.p>

            <div className="flex justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate("/")}
                className="flex items-center bg-[#617C58] hover:bg-[#4e6346] text-white py-3 px-6 rounded-md transition-colors duration-300 font-medium"
              >
                Return to Homepage
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full bg-white rounded-lg shadow-xl overflow-hidden"
      >
        <div className="relative bg-gradient-to-br from-gray-900 via-black/80 to-gray-800 p-8 flex flex-col items-center justify-center text-center">
          <div className="absolute inset-0 bg-[url('/assets/hazelspaybg.jpg')] bg-cover bg-center opacity-30 pointer-events-none"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-5 left-5 w-32 h-32 bg-[#617C58]/30 rounded-full blur-3xl animate-pulse-slow"></div>
            <div className="absolute bottom-5 right-5 w-40 h-40 bg-white/25 rounded-full blur-3xl animate-pulse-slow"></div>
          </div>

          <motion.div
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="relative z-10"
          >
            <HiCreditCard className="w-16 h-16 text-white" />
          </motion.div>
          <motion.h1
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="text-2xl font-bold mt-4 text-white relative z-10"
          >
            Payment Confirmation
          </motion.h1>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="text-gray-300 mt-2 relative z-10"
          >
            {qrDetails.qrCodeName}
          </motion.p>
        </div>

        <div className="p-8">
          <div className="space-y-6 mb-8">
            <div className="flex items-center">
              <HiUser className="w-5 h-5 text-[#617C58] mr-3" />
              <div>
                <p className="text-sm text-gray-500">Merchant</p>
                <p className="font-medium">
                  {qrDetails.firstName} {qrDetails.lastName}
                </p>
              </div>
            </div>

            <div className="flex items-center">
              <HiOfficeBuilding className="w-5 h-5 text-[#617C58] mr-3" />
              <div>
                <p className="text-sm text-gray-500">Company</p>
                <p className="font-medium">{qrDetails.companyName}</p>
              </div>
            </div>

            <div className="flex items-center">
              <HiTag className="w-5 h-5 text-[#617C58] mr-3" />
              <div>
                <p className="text-sm text-gray-500">QR Code</p>
                <p className="font-medium">{qrDetails.qrCodeName}</p>
              </div>
            </div>

            <div className="flex items-center">
              <HiCurrencyDollar className="w-5 h-5 text-[#617C58] mr-3" />
              <div>
                <p className="text-sm text-gray-500">Currency</p>
                <p className="font-medium">{qrDetails.currency}</p>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label
                htmlFor="userName"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Your Name
              </label>
              <input
                type="text"
                id="userName"
                value={userName}
                onChange={handleUserNameChange}
                className="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#617C58]"
                placeholder="John Doe"
                required
              />
            </div>

            <div>
              <label
                htmlFor="userEmail"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Your Email
              </label>
              <input
                type="email"
                id="userEmail"
                value={userEmail}
                onChange={handleUserEmailChange}
                className="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#617C58]"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <label
                htmlFor="amount"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Payment Amount ({qrDetails.currency})
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">
                    {qrDetails.currency === "EUR"
                      ? "€"
                      : qrDetails.currency === "USD"
                      ? "$"
                      : "£"}
                  </span>
                </div>
                <input
                  type="text"
                  id="amount"
                  value={amount}
                  onChange={handleAmountChange}
                  className="w-full pl-8 pr-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#617C58] text-lg"
                  placeholder="0.00"
                  required
                />
              </div>
              {qrDetails.defaultAmount && (
                <p className="mt-1 text-sm text-gray-500">
                  Default amount: {qrDetails.defaultAmount} {qrDetails.currency}
                </p>
              )}
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className={`w-full py-3 rounded-md text-white font-semibold transition ${
                isSubmitting
                  ? "bg-gray-400 cursor-not-allowed"
                  : "bg-[#617C58] hover:bg-[#4e6346] cursor-pointer"
              }`}
            >
              {isSubmitting ? (
                <div className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Processing Payment...
                </div>
              ) : (
                "Pay Now"
              )}
            </button>
          </form>
        </div>
      </motion.div>
      <ToastContainer />
    </div>
  );
};

export default StaticQrPayment;
