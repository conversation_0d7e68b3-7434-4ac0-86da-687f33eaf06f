import { useState, useEffect, useRef } from "react";
import { HiChevronLeft, HiChevronRight } from "react-icons/hi";
import { motion, useScroll, useTransform } from "framer-motion";
import { FaQuoteLeft, FaStar } from "react-icons/fa";

function CompanyPage() {
  // Parallax effect for the hero section
  const heroRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"],
  });
  const y = useTransform(scrollYProgress, [0, 1], ["0%", "30%"]);
  const opacity = useTransform(scrollYProgress, [0, 0.8], [1, 0.3]);
  const timelineData = [
    {
      month: "February",
      year: "2025",
      headline: "HazelsOne Founded",
      event:
        "HazelsOne was established as a creative digital company focused on innovative payment solutions and digital experiences.",
    },
    {
      month: "March",
      year: "2025",
      headline: "Website Launch",
      event:
        "Launched the official HazelsOne website, showcasing our products and services.",
    },
    {
      month: "April",
      year: "2025",
      headline: "First Client Partnership",
      event:
        "Secured our first major client partnership, marking the beginning of our growth journey.",
    },
    {
      month: "May",
      year: "2025",
      headline: "HazelPay Concept",
      event:
        "Developed the initial concept for HazelPay, our flagship mobile payment solution.",
    },
  ];

  const values = [
    {
      title: "Innovation",
      description:
        "We constantly push boundaries to create digital solutions that transform how businesses operate and grow.",
    },
    {
      title: "Simplicity",
      description:
        "We believe in making complex technology accessible through intuitive design and user-friendly experiences.",
    },
    {
      title: "Reliability",
      description:
        "Our customers trust us to deliver consistent, secure, and dependable solutions they can count on.",
    },
    {
      title: "Collaboration",
      description:
        "We work closely with our clients and partners to create solutions that truly meet their needs and exceed expectations.",
    },
  ];

  // State for the current slider index
  const [currentIndex, setCurrentIndex] = useState(0);

  // Detect if we are in mobile view (adjust the breakpoint as needed)
  const [isMobile, setIsMobile] = useState(false);
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Use 1 visible card on mobile and 3 on larger screens
  const visibleItems = isMobile ? 1 : 3;

  const prevSlide = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? timelineData.length - visibleItems : prevIndex - 1
    );
  };

  const nextSlide = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex >= timelineData.length - visibleItems ? 0 : prevIndex + 1
    );
  };

  return (
    <>
      <section
        ref={heroRef}
        className="relative w-full h-[60vh] flex items-center justify-center overflow-hidden"
      >
        <motion.div
          className="absolute inset-0 w-full h-full bg-black/40 z-10"
          style={{ opacity }}
        />
        <motion.div
          className="absolute inset-0 w-full h-full bg-cover bg-center"
          style={{
            backgroundImage: "url('/assets/company-bg.jpg')",
            y,
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        />
        <div className="relative z-20 text-center text-white px-6 max-w-4xl">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-5xl md:text-6xl font-bold mb-6 drop-shadow-lg"
          >
            Our Company
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl md:text-2xl max-w-3xl mx-auto leading-relaxed drop-shadow-md"
          >
            Creating innovative digital experiences and payment solutions that
            empower businesses to thrive in the digital economy
          </motion.p>
          <motion.button
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="mt-8 bg-[#617C58] hover:bg-[#4e6346] text-white py-3 px-8 rounded-full text-lg font-medium transition-all hover:scale-105 shadow-lg"
            onClick={() => (window.location.href = "/joinus")}
          >
            Join Our Journey
          </motion.button>
        </div>
      </section>

      <section className="w-full py-24 px-6 md:px-12 text-center bg-gray-50">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true, margin: "-100px" }}
          className="max-w-5xl mx-auto"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-[#617C58] mb-4">
            Our Journey
          </h2>
          <div className="w-24 h-1 bg-[#617C58] mx-auto mb-8"></div>
          <p className="text-gray-600 max-w-3xl mx-auto mt-4 text-base md:text-lg leading-relaxed">
            Since our founding, HazelsOne has been dedicated to creating
            innovative digital solutions that help businesses connect with their
            customers and streamline their operations.
          </p>

          {/* Timeline slider with improved visuals */}
          <div className="relative max-w-5xl mx-auto mt-16 overflow-hidden h-auto md:h-80">
            <button
              onClick={prevSlide}
              className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white p-4 rounded-full shadow-lg hover:bg-gray-100 transition z-10 border border-gray-200"
            >
              <HiChevronLeft className="text-[#617C58]" size={28} />
            </button>
            <motion.div
              className={`flex ${
                isMobile ? "space-x-0" : "space-x-6"
              } h-full items-center`}
              animate={{ x: `-${currentIndex * (isMobile ? 100 : 33)}%` }}
              transition={{ type: "spring", stiffness: 100, damping: 20 }}
            >
              {timelineData.map((item, index) => (
                <motion.div
                  key={index}
                  className={`relative flex flex-col items-center flex-shrink-0 transition-all duration-300 ${
                    isMobile
                      ? "w-full h-auto"
                      : "w-[50%] md:w-[32%] h-64 md:h-72"
                  }`}
                  whileHover={{ scale: 1.03 }}
                >
                  <div className="absolute inset-0 bg-white rounded-xl shadow-lg border border-gray-100 p-6"></div>

                  <div className="relative z-10 flex flex-col items-center h-full w-full pt-6">
                    <div className="w-16 h-16 bg-[#617C58]/10 rounded-full flex items-center justify-center mb-4">
                      <h3 className="text-[#617C58] font-bold text-xl">
                        {item.year}
                      </h3>
                    </div>

                    <h4 className="text-[#617C58] font-semibold text-xl mb-1">
                      {item.month}
                    </h4>

                    <div className="w-12 h-0.5 bg-[#617C58] my-3"></div>

                    <h3 className="text-gray-800 font-bold text-xl mb-3">
                      {item.headline}
                    </h3>

                    <p className="text-gray-600 text-base px-2 text-center">
                      {item.event}
                    </p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
            <button
              onClick={nextSlide}
              className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-white p-4 rounded-full shadow-lg hover:bg-gray-100 transition z-10 border border-gray-200"
            >
              <HiChevronRight className="text-[#617C58]" size={28} />
            </button>

            {/* Timeline indicator dots */}
            <div className="flex justify-center space-x-2 mt-8">
              {timelineData.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-3 h-3 rounded-full transition-all ${
                    index === currentIndex ? "bg-[#617C58] w-6" : "bg-gray-300"
                  }`}
                />
              ))}
            </div>
          </div>
        </motion.div>
      </section>

      <section className="w-full py-24 px-6 md:px-12 bg-white">
        <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true, margin: "-100px" }}
            className="relative"
          >
            <div className="absolute -top-4 -left-4 w-24 h-24 bg-[#617C58]/10 rounded-lg z-0"></div>
            <div className="absolute -bottom-4 -right-4 w-24 h-24 bg-[#617C58]/10 rounded-lg z-0"></div>
            <img
              src="/assets/company1.jpg"
              alt="HazelsOne Team"
              className="w-full h-auto rounded-lg shadow-xl relative z-10 object-cover"
              style={{ maxHeight: "500px" }}
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Who We Are
            </h2>
            <div className="w-20 h-1 bg-[#617C58] mb-8"></div>

            <div className="text-gray-600 text-base md:text-lg leading-relaxed space-y-6">
              <p>
                HazelsOne is a creative digital company dedicated to innovative
                design and technology solutions. As a product of EftaaPay, we
                combine artistic vision with technical expertise to deliver
                exceptional digital experiences and payment solutions that help
                businesses thrive in the digital economy.
              </p>

              <p>
                Our team of passionate designers, developers, and strategists
                work together to create intuitive, user-friendly products that
                solve real business challenges. From mobile payment solutions
                like HazelPay to comprehensive point-of-sale systems like
                HazelPOS, we're committed to helping businesses of all sizes
                embrace digital transformation.
              </p>

              <div className="pt-4">
                <button className="bg-[#617C58] hover:bg-[#4e6346] text-white py-3 px-8 rounded-lg text-lg font-medium transition-all hover:shadow-lg flex items-center space-x-2 group">
                  <span>Learn More About Our Team</span>
                  <span className="transform group-hover:translate-x-1 transition-transform">
                    →
                  </span>
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      <section className="w-full py-24 px-6 md:px-12 bg-gradient-to-br from-[#617C58] to-[#3d4e37] text-white relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div
            className="absolute top-0 left-0 w-full h-full"
            style={{
              backgroundImage:
                'url(\'data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="1"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\')',
              backgroundSize: "60px 60px",
            }}
          ></div>
        </div>

        <div className="max-w-6xl mx-auto relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true, margin: "-100px" }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Our Vision & Mission
            </h2>
            <div className="w-24 h-1 bg-white mx-auto"></div>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true, margin: "-100px" }}
              className="space-y-12"
            >
              <div className="bg-white/10 p-8 rounded-xl backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-1">
                <h3 className="text-2xl font-bold mb-4 flex items-center">
                  <span className="w-10 h-10 bg-white text-[#617C58] rounded-full flex items-center justify-center mr-3 text-xl">
                    V
                  </span>
                  Vision
                </h3>
                <p className="text-gray-100 text-lg leading-relaxed pl-14">
                  To transform how businesses connect with their customers
                  through innovative digital solutions that drive growth and
                  create meaningful experiences.
                </p>
              </div>

              <div className="bg-white/10 p-8 rounded-xl backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-1">
                <h3 className="text-2xl font-bold mb-4 flex items-center">
                  <span className="w-10 h-10 bg-white text-[#617C58] rounded-full flex items-center justify-center mr-3 text-xl">
                    M
                  </span>
                  Mission
                </h3>
                <p className="text-gray-100 text-lg leading-relaxed pl-14">
                  Create intuitive, reliable digital experiences and payment
                  solutions that empower businesses to grow and succeed in an
                  increasingly digital world, while providing exceptional value
                  and service.
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true, margin: "-100px" }}
              className="bg-white/10 p-8 rounded-xl backdrop-blur-sm border border-white/20"
            >
              <h3 className="text-2xl font-bold mb-6 flex items-center">
                <span className="w-10 h-10 bg-white text-[#617C58] rounded-full flex items-center justify-center mr-3 text-xl">
                  A
                </span>
                Our Approach
              </h3>
              <ul className="space-y-6">
                <li className="flex items-start group">
                  <span className="bg-white/20 w-10 h-10 rounded-full flex items-center justify-center mr-4 text-xl group-hover:bg-white group-hover:text-[#617C58] transition-all">
                    ✓
                  </span>
                  <div>
                    <h4 className="font-bold text-lg mb-1">Listen</h4>
                    <p className="text-gray-100">
                      We listen carefully to understand your unique business
                      needs and challenges
                    </p>
                  </div>
                </li>
                <li className="flex items-start group">
                  <span className="bg-white/20 w-10 h-10 rounded-full flex items-center justify-center mr-4 text-xl group-hover:bg-white group-hover:text-[#617C58] transition-all">
                    ✓
                  </span>
                  <div>
                    <h4 className="font-bold text-lg mb-1">Design</h4>
                    <p className="text-gray-100">
                      We design solutions that are both beautiful and
                      functional, with the user experience at the center
                    </p>
                  </div>
                </li>
                <li className="flex items-start group">
                  <span className="bg-white/20 w-10 h-10 rounded-full flex items-center justify-center mr-4 text-xl group-hover:bg-white group-hover:text-[#617C58] transition-all">
                    ✓
                  </span>
                  <div>
                    <h4 className="font-bold text-lg mb-1">Build</h4>
                    <p className="text-gray-100">
                      We build with scalability and future growth in mind, using
                      the latest technologies
                    </p>
                  </div>
                </li>
                <li className="flex items-start group">
                  <span className="bg-white/20 w-10 h-10 rounded-full flex items-center justify-center mr-4 text-xl group-hover:bg-white group-hover:text-[#617C58] transition-all">
                    ✓
                  </span>
                  <div>
                    <h4 className="font-bold text-lg mb-1">Support</h4>
                    <p className="text-gray-100">
                      We provide ongoing support to ensure your continued
                      success and growth
                    </p>
                  </div>
                </li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      <section className="w-full py-24 px-6 md:px-12 text-center bg-gray-50">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true, margin: "-100px" }}
          className="max-w-6xl mx-auto"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Our Core Values
          </h2>
          <div className="w-24 h-1 bg-[#617C58] mx-auto mb-8"></div>

          <p className="text-gray-600 max-w-4xl mx-auto mt-4 text-lg md:text-xl mb-16 leading-relaxed">
            At HazelsOne, our core values guide everything we do, from how we
            design our products to how we interact with our clients and
            partners.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true, margin: "-50px" }}
                whileHover={{ y: -10 }}
                className="flex flex-col items-center text-center p-8 bg-white rounded-xl shadow-lg border border-gray-100 h-full"
              >
                <div className="w-20 h-20 flex items-center justify-center bg-[#617C58] rounded-full mb-6 text-white shadow-lg">
                  <span className="text-white text-2xl font-bold">
                    {index + 1}
                  </span>
                </div>
                <h3 className="text-2xl font-bold text-[#617C58] mb-4">
                  {value.title}
                </h3>
                <div className="w-12 h-0.5 bg-[#617C58]/30 mb-4"></div>
                <p className="text-gray-600 text-lg">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </section>

      {/* Add a testimonials section */}
      <section className="w-full py-24 px-6 md:px-12 bg-white">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true, margin: "-100px" }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              What Our Clients Say
            </h2>
            <div className="w-24 h-1 bg-[#617C58] mx-auto mb-8"></div>
            <p className="text-gray-600 max-w-3xl mx-auto text-lg">
              We're proud to work with businesses that trust us to deliver
              exceptional digital solutions
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "Sarah Johnson",
                role: "CEO, TechVision",
                quote:
                  "HazelsOne transformed our payment processing system, resulting in a 40% increase in customer satisfaction and significantly reduced transaction times.",
                stars: 5,
              },
              {
                name: "Michael Chen",
                role: "Founder, GrowthHub",
                quote:
                  "The team at HazelsOne truly understands our business needs. Their HazelPay integration has streamlined our operations and improved our customer experience.",
                stars: 5,
              },
              {
                name: "Jessica Williams",
                role: "Marketing Director, Elevate Retail",
                quote:
                  "Working with HazelsOne has been a game-changer for our business. Their innovative solutions and responsive support team exceed our expectations.",
                stars: 5,
              },
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true, margin: "-50px" }}
                className="bg-gray-50 p-8 rounded-xl shadow-md border border-gray-100 relative"
              >
                <FaQuoteLeft className="text-[#617C58]/20 text-6xl absolute top-4 left-4" />
                <div className="relative z-10">
                  <div className="flex mb-4">
                    {[...Array(testimonial.stars)].map((_, i) => (
                      <FaStar key={i} className="text-yellow-500" />
                    ))}
                  </div>
                  <p className="text-gray-700 mb-6 italic">
                    "{testimonial.quote}"
                  </p>
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-[#617C58]/20 rounded-full flex items-center justify-center text-[#617C58] font-bold text-xl">
                      {testimonial.name.charAt(0)}
                    </div>
                    <div className="ml-4 text-left">
                      <h4 className="font-bold text-gray-900">
                        {testimonial.name}
                      </h4>
                      <p className="text-gray-600 text-sm">
                        {testimonial.role}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="w-full py-24 px-6 md:px-12 bg-gray-900 text-white relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div
            className="absolute top-0 left-0 w-full h-full"
            style={{
              backgroundImage:
                'url(\'data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="1"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\')',
              backgroundSize: "60px 60px",
            }}
          ></div>
        </div>

        <div className="max-w-6xl mx-auto relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true, margin: "-100px" }}
              className="text-left"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Ready to Transform Your Business?
              </h2>
              <div className="w-20 h-1 bg-[#617C58] mb-8"></div>
              <p className="text-xl leading-relaxed mb-10 text-gray-300">
                Let's work together to create digital solutions that help your
                business thrive in today's competitive landscape. Our team is
                ready to bring your vision to life.
              </p>

              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  onClick={() => (window.location.href = "/joinus")}
                  className="bg-[#617C58] hover:bg-[#4e6346] text-white py-4 px-8 rounded-lg text-lg font-medium transition-all shadow-lg flex items-center justify-center"
                >
                  <span>Get in Touch</span>
                  <span className="ml-2">→</span>
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  onClick={() => (window.location.href = "/services")}
                  className="bg-transparent hover:bg-white/10 text-white border border-white py-4 px-8 rounded-lg text-lg font-medium transition-all flex items-center justify-center"
                >
                  <span>Explore Our Services</span>
                </motion.button>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true, margin: "-100px" }}
              className="relative"
            >
              <div className="absolute -top-6 -right-6 w-32 h-32 bg-[#617C58]/20 rounded-full z-0"></div>
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-[#617C58]/20 rounded-full z-0"></div>

              <div className="bg-white/10 backdrop-blur-sm p-8 rounded-xl border border-white/10 relative z-10">
                <h3 className="text-2xl font-bold mb-6">Contact Us</h3>
                <div className="space-y-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-[#617C58] rounded-full flex items-center justify-center">
                      <span className="text-white">📧</span>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Email</p>
                      <p className="text-white font-medium">
                        <EMAIL>
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-[#617C58] rounded-full flex items-center justify-center">
                      <span className="text-white">📱</span>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Phone</p>
                      <p className="text-white font-medium">
                        +****************
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-[#617C58] rounded-full flex items-center justify-center">
                      <span className="text-white">🌐</span>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Social</p>
                      <div className="flex space-x-4 mt-2">
                        <a
                          href="#"
                          className="text-white hover:text-[#617C58] transition-colors"
                        >
                          <span className="text-xl">📘</span>
                        </a>
                        <a
                          href="#"
                          className="text-white hover:text-[#617C58] transition-colors"
                        >
                          <span className="text-xl">📷</span>
                        </a>
                        <a
                          href="#"
                          className="text-white hover:text-[#617C58] transition-colors"
                        >
                          <span className="text-xl">🐦</span>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </>
  );
}

export default CompanyPage;
