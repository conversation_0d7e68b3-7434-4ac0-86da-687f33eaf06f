import React from "react";

const TermsOfUse: React.FC = () => {
  return (
    <section className="w-full mt-10 py-16 px-6 md:px-12 lg:px-28 xl:px-36 text-gray-800">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
          Terms of Use
        </h1>
        <p className="text-sm text-gray-500 mt-2">
          Effective Date: 25 February 2025
        </p>
      </div>

      {/* Table of Contents */}
      <div className="mt-8 max-w-3xl mx-auto bg-gray-100 p-6 rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Table of Contents
        </h2>
        <ul className="space-y-2 text-sm md:text-base text-gray-700">
          {[
            "Legal Information",
            "Acceptance of Terms",
            "Intellectual Property",
            "Use of Website",
            "User Accounts",
            "Digital Services",
            "Limitation of Liability",
            "External Links",
            "Data Protection and Privacy",
            "Modification of Terms",
            "Governing Law",
          ].map((item, index) => (
            <li key={index} className="hover:text-green-700 transition">
              <a href={`#section-${index}`} className="underline">
                {index + 1}. {item}
              </a>
            </li>
          ))}
        </ul>
      </div>

      {/* Terms Content */}
      <div className="mt-12 max-w-3xl mx-auto space-y-8">
        {/* Section 1: Legal Information */}
        <div id="section-0">
          <h2 className="text-xl font-semibold text-gray-900">
            1. Legal Information
          </h2>
          <p className="text-sm md:text-base text-gray-600 mt-2">
            This website is owned and operated by Hazels One, a creative digital
            company and a product of{" "}
            <a
              href="https://eftaapay.com"
              className="text-blue-600 hover:underline"
            >
              EftaaPay
            </a>
            . Our registered office is located at:
          </p>
          {/* <p className="mt-2 text-sm md:text-base text-gray-800 font-semibold">
            [Your Office Address Here]
          </p> */}
          <p className="mt-2 text-sm md:text-base text-gray-600">
            For inquiries, please contact us at:{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-blue-600 hover:underline"
            >
              <EMAIL>
            </a>
          </p>
        </div>

        {/* Sections 2-11 */}
        {[
          {
            title: "2. Acceptance of Terms",
            content:
              "By accessing this website, you acknowledge that you have read, understood, and agreed to be bound by these Terms of Use, our Privacy Policy, and any additional policies posted on this website.",
          },
          {
            title: "3. Intellectual Property",
            content:
              "All content on this website, including but not limited to text, graphics, logos, icons, images, and software, is the property of Hazels One or its licensors. The content is protected by copyright, trademark, and other intellectual property laws.",
          },
          {
            title: "4. Use of Website",
            content:
              "You agree to use this website only for lawful purposes and in a manner that does not infringe upon the rights of others or restrict or inhibit their use and enjoyment of the website.",
          },
          {
            title: "5. User Accounts",
            content:
              "To access certain services, you may be required to create an account. You are responsible for maintaining the confidentiality of your account information and for all activities that occur under your account.",
          },
          {
            title: "6. Digital Services",
            content:
              "Hazels One offers creative digital solutions, including payment processing services powered by EftaaPay. By using our website and services, you agree to comply with the terms and conditions outlined during your engagement with us.",
          },
          {
            title: "7. Limitation of Liability",
            content:
              "Hazels One shall not be liable for any indirect, incidental, or consequential damages arising out of or related to the use of this website or our services. This includes, but is not limited to, loss of data, revenue, or profits.",
          },
          {
            title: "8. External Links",
            content:
              "This website may contain links to third-party websites. Hazels One is not responsible for the content, policies, or practices of such external websites. Access to these websites is at your own risk.",
          },
          {
            title: "9. Data Protection and Privacy",
            content:
              "We process personal data in accordance with applicable data protection laws. As a product of EftaaPay, we adhere to strict data protection standards for all payment processing activities. Please refer to our Privacy Policy for more information.",
          },
          {
            title: "10. Modification of Terms",
            content:
              "Hazels One reserves the right to modify these Terms of Use at any time. Changes will be posted on this page, and your continued use of the website constitutes acceptance of the modified terms.",
          },
          {
            title: "11. Governing Law",
            content:
              "These Terms of Use are governed by and construed in accordance with the laws of England and Wales. Any disputes arising from these terms will be subject to the exclusive jurisdiction of the courts of England and Wales.",
          },
        ].map((section, index) => (
          <div key={index} id={`section-${index + 1}`}>
            <h2 className="text-xl font-semibold text-gray-900">
              {section.title}
            </h2>
            <p className="text-sm md:text-base text-gray-600 mt-2">
              {section.content}
            </p>
          </div>
        ))}
      </div>
    </section>
  );
};

export default TermsOfUse;
