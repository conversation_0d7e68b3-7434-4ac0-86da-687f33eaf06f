import React from "react";
import { useNavigate } from "react-router-dom";

const HazelPay: React.FC = () => {
  const navigate = useNavigate();

  const handleNavigate = (path: string) => {
    navigate(path);
    window.scrollTo(0, 0);
  };

  interface Feature {
    title: string;
    desc: string;
    img: string;
  }

  interface UseCase {
    scenario: string;
    details: string;
    img: string;
  }

  const features: Feature[] = [
    {
      title: "Mobility & Versatility",
      desc: "Take payments anywhere with offline mode and contactless support for cards, phones, and wearables—unstoppable business flow.",
      img: "/assets/hp1.jpg",
    },
    {
      title: "Seamless Transactions",
      desc: "Lightning-fast sub-second processing for Visa, Mastercard, Apple Pay, and more—effortless for every customer.",
      img: "/assets/hp2.jpg",
    },
    {
      title: "User-Friendly Design",
      desc: "Rugged yet intuitive—drop-proof with an instant-click touchscreen, designed for zero learning curve.",
      img: "/assets/hp3.jpg",
    },
    {
      title: "Robust Security",
      desc: "Fortified with end-to-end encryption and biometric locks—your funds are untouchable.",
      img: "/assets/hp4.jpg",
    },
  ];

  const useCases: UseCase[] = [
    {
      scenario: "Market Day",
      details:
        "Run a stall offline all day, sync seamlessly later—no signal, no problem.",
      img: "/assets/hp5.jpeg",
    },
    {
      scenario: "Food Truck",
      details:
        "Tap-to-pay during peak hours, monitor stats on the fly—stay ahead of the rush.",
      img: "/assets/hp6.jpg",
    },
    {
      scenario: "Delivery",
      details:
        "Finalize payments at the door with cards or wallets, instant receipts included.",
      img: "/assets/hp7.jpg",
    },
    {
      scenario: "Freelance",
      details:
        "Bill and collect on-site with payments as mobile as your workflow.",
      img: "/assets/hp8.jpg",
    },
    {
      scenario: "Taxi Services",
      details:
        "Secure fare collection with digital receipts—perfect for drivers on the go.",
      img: "/assets/hp9.jpg",
    },
  ];

  return (
    <main className="font-['SF_Pro_Display','Arial',sans-serif] text-gray-900 leading-relaxed antialiased">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-900 via-black/80 to-gray-800 pt-28 pb-36 px-6 overflow-hidden">
        <div className="absolute inset-0 bg-[url('/assets/hazelspaybg.jpg')] bg-cover bg-center opacity-40 pointer-events-none"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-10 left-10 w-72 h-72 bg-[#617C58]/30 rounded-full blur-3xl animate-pulse-slow"></div>
          <div className="absolute bottom-10 right-10 w-96 h-96 bg-white/25 rounded-full blur-3xl animate-pulse-slow"></div>
        </div>

        <div className="max-w-7xl mx-auto relative z-10 flex flex-col items-center text-center">
          <h1 className="text-5xl md:text-7xl font-extrabold tracking-tight text-white animate-fade-in-down">
            HazelPay
          </h1>
          <p className="text-xl md:text-2xl text-gray-200 mt-4 mb-12 max-w-4xl leading-snug animate-fade-in-up">
            Revolutionize mobile payments with cutting-edge speed and security.
          </p>
          <div className="w-full max-w-5xl mx-auto rounded-2xl overflow-hidden shadow-2xl mb-12 transform hover:scale-[1.03] transition-transform duration-700 ease-out">
            <img
              src="/assets/hazelspaybg.jpg"
              alt="HazelPay Terminal"
              className="w-full h-auto object-cover"
            />
          </div>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto mb-10 animate-fade-in-up">
            Compact. Powerful. Tailored for pop-ups, food trucks, deliveries,
            taxi services, and on-site payments—wherever your hustle takes you.
          </p>
          <button
            onClick={() => handleNavigate("/joinus")}
            className="bg-[#617C58] hover:bg-[#7A7AD1] text-white py-4 px-10 rounded-full font-semibold text-lg shadow-xl transform hover:scale-105 transition-all duration-400 ease-in-out animate-fade-in-up"
          >
            Explore HazelPay
          </button>
        </div>
      </section>
      <section className="bg-gradient-to-b from-white to-gray-50 py-32 px-6">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-5xl md:text-6xl font-extrabold text-center tracking-tight text-gray-900 animate-fade-in-down">
            HazelPay’s Game-Changing Features
          </h2>
          {features.map((feature, index) => (
            <div
              key={index}
              className={`flex flex-col mt-16 ${
                index % 2 === 0 ? "lg:flex-row" : "lg:flex-row-reverse"
              } items-center gap-12 mb-20 last:mb-0 animate-fade-in-up`}
              style={{ animationDelay: `${index * 300}ms` }}
            >
              <div className="lg:w-1/2">
                <div className="relative w-full h-96 rounded-2xl overflow-hidden shadow-2xl transform hover:scale-[1.02] transition-transform duration-700">
                  <div className="w-full h-96 flex items-center justify-center bg-gray-100">
                    <img
                      src={feature.img}
                      alt={feature.title}
                      className="w-full h-full object-cover"
                    />{" "}
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-br from-[#617C58]/20 to-transparent opacity-0 hover:opacity-30 transition-opacity duration-500"></div>
                </div>
              </div>
              <div className="lg:w-1/2">
                <h3 className="text-3xl md:text-4xl font-bold mb-6 tracking-tight text-gray-900">
                  {feature.title}
                </h3>
                <p className="text-lg md:text-xl text-gray-600 mb-8 leading-relaxed">
                  {feature.desc}
                </p>
                <button
                  onClick={() => handleNavigate("/joinus")}
                  className="text-[#617C58] font-semibold text-lg hover:underline transition-all duration-300"
                >
                  Learn More →
                </button>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="bg-white py-32 px-6">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-5xl md:text-6xl font-extrabold text-center tracking-tight text-gray-900 animate-fade-in-down">
            Built for Your World
          </h2>
          <div className="relative mt-16">
            {useCases.map((use, index) => (
              <div
                key={index}
                className="relative flex flex-col lg:flex-row items-center gap-16 mb-24 last:mb-0 animate-fade-in-up"
                style={{ animationDelay: `${index * 300}ms` }}
              >
                <div className="lg:w-2/3">
                  <div className="relative w-full h-full rounded-3xl overflow-hidden shadow-2xl transform hover:scale-[1.02] transition-transform duration-700">
                    <div className="w-full h-96 flex items-center justify-center bg-gray-100">
                      <img
                        src={use.img}
                        alt={use.scenario}
                        className="w-full h-full object-cover"
                      />{" "}
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    <div className="absolute bottom-8 left-8 right-8 text-white">
                      <h3 className="text-3xl font-bold mb-4 tracking-tight">
                        {use.scenario}
                      </h3>
                      <p className="text-lg leading-relaxed">{use.details}</p>
                    </div>
                  </div>
                </div>
                <div className="lg:w-1/3 flex justify-center lg:justify-start">
                  <div className="relative w-64 h-64 bg-gradient-to-br from-[#617C58]/20 to-white rounded-full flex items-center justify-center shadow-xl">
                    <div className="text-6xl font-extrabold text-[#617C58] transform hover:scale-110 transition-transform duration-500">
                      {index + 1}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Mobile Experience Section */}
      <section className="bg-white py-28 px-6">
        <div className="max-w-7xl mx-auto flex flex-col lg:flex-row items-center gap-16">
          <div className="lg:w-1/2">
            <h2 className="text-4xl md:text-5xl font-extrabold mb-8 tracking-tight text-gray-900 animate-fade-in-down">
              Seamless. Mobile. Revolutionary.
            </h2>
            <p className="text-lg text-gray-600 mb-6 leading-relaxed animate-fade-in-up">
              Unlock the future of payments with a sleek, secure app designed
              for the mobile entrepreneur. Process transactions with unmatched
              ease.
            </p>
            <ul className="space-y-5 mb-8">
              <li className="flex items-start animate-fade-in-up">
                <span className="text-[#617C58] mr-4 text-2xl">✔</span>
                <span className="text-base text-gray-600">
                  <strong>Peak Efficiency:</strong> Syncs with your phone for
                  rapid transactions and low battery drain.
                </span>
              </li>
              <li className="flex items-start animate-fade-in-up">
                <span className="text-[#617C58] mr-4 text-2xl">✔</span>
                <span className="text-base text-gray-600">
                  <strong>Uninterrupted Connectivity:</strong> Thrives in
                  low-signal zones with device-powered reliability.
                </span>
              </li>
              <li className="flex items-start animate-fade-in-up">
                <span className="text-[#617C58] mr-4 text-2xl">✔</span>
                <span className="text-base text-gray-600">
                  <strong>Top-Tier Security:</strong> Features advanced
                  encryption and biometric authentication.
                </span>
              </li>
              <li className="flex items-start animate-fade-in-up">
                <span className="text-[#617C58] mr-4 text-2xl">✔</span>
                <span className="text-base text-gray-600">
                  <strong>Effortless Design:</strong> Intuitive interface—tap
                  and pay, no training needed.
                </span>
              </li>
            </ul>
            <p className="text-lg text-gray-600 animate-fade-in-up">
              Perfect for pop-ups, food trucks, deliveries, taxis, and
              beyond—HazelPay redefines mobility with sophistication.
            </p>
          </div>
          <div className="lg:w-1/2">
            <div className="relative w-full h-96 rounded-2xl overflow-hidden shadow-2xl transform hover:scale-105 transition-transform duration-500">
              <img
                src="/assets/hazelappss.jpg"
                alt="HazelPay Mobile App Screenshot"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-br from-[#617C58]/20 to-transparent opacity-0 hover:opacity-20 transition-opacity duration-500"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="relative bg-gradient-to-br from-gray-900 via-black/80 to-gray-800 py-36 px-6 text-white overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>

        <div className="relative max-w-7xl mx-auto flex flex-col items-center text-center">
          <h2 className="text-5xl md:text-7xl font-extrabold mb-8 tracking-tight animate-fade-in-down">
            Elevate Your Payments with HazelPay
          </h2>
          <p className="text-xl md:text-2xl max-w-4xl mx-auto mb-12 leading-relaxed text-gray-200 animate-fade-in-up">
            Empower your business with a mobile payment solution that combines
            speed, security, and style—unmatched anywhere.
          </p>
          <button
            onClick={() => handleNavigate("/joinus")}
            className="relative bg-[#617C58] hover:bg-[#7A7AD1] text-white py-5 px-12 rounded-full font-semibold text-xl shadow-2xl transform hover:scale-105 transition-all duration-500 ease-out animate-fade-in-up"
          >
            <span className="relative z-10">Start Your Journey</span>
            <span className="absolute inset-0 rounded-full bg-gradient-to-r from-transparent via-white/30 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-700"></span>
          </button>
          <p className="mt-10 text-lg text-gray-400 animate-fade-in-up">
            Curious?{" "}
            <a
              href="/demo"
              className="underline hover:text-[#617C58] transition-colors duration-300"
            >
              Schedule a Demo
            </a>
          </p>
        </div>
      </section>
    </main>
  );
};

export default HazelPay;
