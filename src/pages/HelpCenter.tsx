import React, { useState, useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import {
  HiChevronDown,
  HiOutlineSearch,
  HiOutlineMail,
  HiOutlinePhone,
  HiOutlineDocumentText,
  HiOutlineLightBulb,
  HiOutlineQuestionMarkCircle,
} from "react-icons/hi";

const HelpCenter: React.FC = () => {
  // Parallax effect for the hero section
  const heroRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"],
  });
  const y = useTransform(scrollYProgress, [0, 1], ["0%", "30%"]);
  const opacity = useTransform(scrollYProgress, [0, 0.8], [1, 0.3]);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeCategory, setActiveCategory] = useState("all");
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

  // FAQ categories
  const categories = [
    { id: "all", name: "All Questions" },
    { id: "account", name: "Account" },
    { id: "payments", name: "Payments" },
    { id: "products", name: "Products" },
    { id: "technical", name: "Technical" },
  ];

  // FAQ items
  const faqItems = [
    {
      id: 1,
      question: "How do I create an account?",
      answer:
        "To create an account, click on the 'Sign Up' button in the top right corner of our website. Fill in your details, verify your email address, and you're all set!",
      category: "account",
    },
    {
      id: 2,
      question: "What payment methods do you accept?",
      answer:
        "We accept all major credit cards, debit cards, and bank transfers. For businesses, we also offer invoice-based payment options.",
      category: "payments",
    },
    {
      id: 3,
      question: "How can I reset my password?",
      answer:
        "Click on the 'Forgot Password' link on the login page. Enter your email address, and we'll send you instructions to reset your password.",
      category: "account",
    },
    {
      id: 4,
      question: "Is my payment information secure?",
      answer:
        "Yes, all payment information is encrypted and processed securely. We use industry-standard security protocols to protect your data.",
      category: "payments",
    },
    {
      id: 5,
      question: "What is HazelPay?",
      answer:
        "HazelPay is our mobile payment solution that allows businesses to accept payments anywhere, anytime. It's secure, fast, and easy to integrate with your existing systems.",
      category: "products",
    },
    {
      id: 6,
      question: "How do I update the app?",
      answer:
        "Our apps automatically update when new versions are available. If you've disabled automatic updates, you can manually update through your device's app store.",
      category: "technical",
    },
    {
      id: 7,
      question: "What should I do if the app crashes?",
      answer:
        "First, try restarting the app. If the issue persists, restart your device. Make sure you have the latest version installed. If problems continue, contact our support team.",
      category: "technical",
    },
    {
      id: 8,
      question: "How can I integrate HazelPOS with my existing systems?",
      answer:
        "HazelPOS offers API integration with most major business management systems. Check our developer documentation or contact our support team for specific integration guides.",
      category: "products",
    },
  ];

  // Filter FAQs based on search query and active category
  const filteredFaqs = faqItems.filter((faq) => {
    const matchesSearch =
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory =
      activeCategory === "all" || faq.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  // Support resources
  const supportResources = [
    {
      title: "Email Support",
      description: "Get help from our support team",
      icon: <HiOutlineMail className="w-8 h-8 text-[#617C58]" />,
      action: "<EMAIL>",
      link: "mailto:<EMAIL>",
    },
    {
      title: "Phone Support",
      description: "Talk to a representative",
      icon: <HiOutlinePhone className="w-8 h-8 text-[#617C58]" />,
      action: "Call Us",
      link: "tel:+1234567890",
    },
    {
      title: "Documentation",
      description: "Browse our guides and tutorials",
      icon: <HiOutlineDocumentText className="w-8 h-8 text-[#617C58]" />,
      action: "View Docs",
      link: "#",
    },
  ];

  // Quick tips
  const quickTips = [
    "Ensure your app is updated to the latest version",
    "Restart your device if you encounter unexpected behavior",
    "Check your internet connection for online features",
    "Clear cache and cookies if experiencing website issues",
    "Use our search function to find specific help topics",
  ];

  return (
    <main className="bg-gray-50 min-h-screen">
      {/* Hero Section with Search */}
      <section
        ref={heroRef}
        className="relative h-[70vh] flex items-center justify-center overflow-hidden"
      >
        {/* Background overlay */}
        <motion.div
          className="absolute inset-0 bg-black/50 z-10"
          style={{ opacity }}
        />

        {/* Background image with parallax effect */}
        <motion.div
          className="absolute inset-0 w-full h-full bg-cover bg-center"
          style={{
            backgroundImage: "url('/assets/help-bg.jpg')",
            y,
            backgroundSize: "cover",
            backgroundPosition: "center 30%",
          }}
        />

        {/* Content */}
        <div className="relative z-20 max-w-5xl mx-auto text-center px-6">
          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            className="inline-block bg-[#617C58]/20 backdrop-blur-sm p-2 px-4 rounded-full text-white text-sm font-medium mb-6 border border-white/20"
          >
            <HiOutlineQuestionMarkCircle className="inline-block mr-2" />
            24/7 Support Available
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.1 }}
            className="text-4xl md:text-6xl font-bold mb-6 text-white drop-shadow-lg"
          >
            How can we help you?
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="text-xl md:text-2xl mb-10 max-w-3xl mx-auto text-white/90 drop-shadow-md"
          >
            Find answers to your questions and get the support you need
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.3 }}
            className="relative max-w-2xl mx-auto"
          >
            <HiOutlineSearch className="absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-500 text-2xl" />
            <input
              type="text"
              placeholder="Search for help..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full py-5 pl-14 pr-5 rounded-full bg-white/95 backdrop-blur-sm text-gray-800 shadow-xl focus:outline-none focus:ring-2 focus:ring-[#617C58] text-lg transition-all"
            />
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-[#617C58] text-white px-6 py-3 rounded-full text-sm font-medium shadow-md hover:bg-[#4e6346] transition-colors"
            >
              Search
            </motion.button>
          </motion.div>

          {/* Popular searches */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.4 }}
            className="mt-6 flex flex-wrap justify-center gap-2"
          >
            <span className="text-white/70 mr-2">Popular:</span>
            {[
              "Password reset",
              "Payment methods",
              "HazelPay",
              "Account setup",
            ].map((term, index) => (
              <button
                key={index}
                onClick={() => setSearchQuery(term)}
                className="text-white/90 hover:text-white bg-white/10 hover:bg-white/20 px-3 py-1 rounded-full text-sm transition-colors"
              >
                {term}
              </button>
            ))}
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24 px-6 bg-white">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true, margin: "-100px" }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <div className="w-24 h-1 bg-[#617C58] mx-auto mb-6"></div>
            <p className="text-gray-600 max-w-3xl mx-auto text-lg">
              Find answers to common questions about our products and services
            </p>
          </motion.div>

          {/* Category Tabs */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.1 }}
            viewport={{ once: true, margin: "-100px" }}
            className="flex flex-wrap justify-center gap-3 mb-12"
          >
            {categories.map((category, index) => (
              <motion.button
                key={category.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                onClick={() => setActiveCategory(category.id)}
                className={`px-6 py-3 rounded-lg text-sm font-medium transition-all ${
                  activeCategory === category.id
                    ? "bg-[#617C58] text-white shadow-md transform -translate-y-1"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200 hover:shadow"
                }`}
              >
                {category.name}
              </motion.button>
            ))}
          </motion.div>

          {/* FAQ Items */}
          <div className="space-y-5 max-w-4xl mx-auto">
            {filteredFaqs.length > 0 ? (
              filteredFaqs.map((faq, index) => (
                <motion.div
                  key={faq.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true, margin: "-50px" }}
                  className="bg-gray-50 rounded-xl shadow-sm overflow-hidden border border-gray-100"
                >
                  <button
                    onClick={() =>
                      setExpandedFaq(expandedFaq === faq.id ? null : faq.id)
                    }
                    className={`w-full px-8 py-5 text-left flex justify-between items-center hover:bg-gray-100 transition-colors ${
                      expandedFaq === faq.id ? "bg-gray-100" : ""
                    }`}
                  >
                    <div className="flex items-center">
                      <span
                        className={`w-8 h-8 rounded-full flex items-center justify-center mr-4 flex-shrink-0 ${
                          expandedFaq === faq.id
                            ? "bg-[#617C58] text-white"
                            : "bg-gray-200 text-gray-600"
                        }`}
                      >
                        <HiOutlineQuestionMarkCircle className="w-5 h-5" />
                      </span>
                      <h3 className="text-lg font-medium text-gray-800">
                        {faq.question}
                      </h3>
                    </div>
                    <HiChevronDown
                      className={`w-6 h-6 text-gray-500 transition-transform duration-300 ${
                        expandedFaq === faq.id
                          ? "transform rotate-180 text-[#617C58]"
                          : ""
                      }`}
                    />
                  </button>
                  {expandedFaq === faq.id && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      transition={{ duration: 0.3 }}
                      className="px-8 py-6 border-t border-gray-200"
                    >
                      <p className="text-gray-700 leading-relaxed pl-12">
                        {faq.answer}
                      </p>
                      <div className="mt-4 pl-12 pt-4 border-t border-gray-100 flex justify-between items-center">
                        <span className="text-sm text-gray-500">
                          Was this helpful?
                        </span>
                        <div className="flex space-x-2">
                          <button className="px-4 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-full text-sm transition-colors">
                            Yes
                          </button>
                          <button className="px-4 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-full text-sm transition-colors">
                            No
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </motion.div>
              ))
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-16 bg-gray-50 rounded-xl"
              >
                <HiOutlineSearch className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500 text-lg mb-4">No results found.</p>
                <p className="text-gray-400">
                  Try a different search term or category.
                </p>
                <button
                  onClick={() => {
                    setSearchQuery("");
                    setActiveCategory("all");
                  }}
                  className="mt-6 px-6 py-2 bg-[#617C58] text-white rounded-lg hover:bg-[#4e6346] transition-colors"
                >
                  Reset Filters
                </button>
              </motion.div>
            )}
          </div>

          {/* Still have questions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true, margin: "-50px" }}
            className="mt-16 text-center"
          >
            <p className="text-gray-600 mb-4">Still have questions?</p>
            <a
              href="mailto:<EMAIL>"
              className="text-[#617C58] font-medium hover:underline"
            >
              Contact our support team
            </a>
          </motion.div>
        </div>
      </section>

      {/* Quick Tips Section */}
      <section className="py-24 px-6 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true, margin: "-100px" }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Quick Support Tips
            </h2>
            <div className="w-24 h-1 bg-[#617C58] mx-auto mb-6"></div>
            <p className="text-gray-600 max-w-3xl mx-auto text-lg">
              Follow these tips to resolve common issues quickly
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {quickTips.map((tip, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true, margin: "-50px" }}
                whileHover={{
                  y: -5,
                  boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)",
                }}
                className="bg-white rounded-xl p-6 shadow-md border border-gray-100 transition-all duration-300"
              >
                <div className="flex items-start">
                  <div className="bg-[#617C58]/10 rounded-full p-3 mr-4">
                    <HiOutlineLightBulb className="text-[#617C58] w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-800 mb-2">
                      Tip #{index + 1}
                    </h3>
                    <p className="text-gray-700">{tip}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.5 }}
            viewport={{ once: true, margin: "-50px" }}
            className="mt-16 text-center"
          >
            <a
              href="#"
              className="inline-flex items-center text-[#617C58] font-medium hover:underline"
            >
              <span>View all support tips</span>
              <svg
                className="w-4 h-4 ml-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 5l7 7-7 7"
                ></path>
              </svg>
            </a>
          </motion.div>
        </div>
      </section>

      {/* Support Resources */}
      <section className="py-24 px-6 bg-white">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true, margin: "-100px" }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Get Support
            </h2>
            <div className="w-24 h-1 bg-[#617C58] mx-auto mb-6"></div>
            <p className="text-gray-600 max-w-3xl mx-auto text-lg">
              Choose the support option that works best for you
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {supportResources.map((resource, index) => (
              <motion.a
                key={index}
                href={resource.link}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true, margin: "-50px" }}
                whileHover={{
                  y: -10,
                  boxShadow: "0 15px 30px -10px rgba(0, 0, 0, 0.1)",
                  backgroundColor:
                    index === 0
                      ? "#f0f7ee"
                      : index === 1
                      ? "#eef4ff"
                      : "#fff8ee",
                }}
                className="bg-white rounded-xl shadow-lg p-8 flex flex-col items-center text-center transition-all duration-300 border border-gray-100 relative overflow-hidden h-full"
              >
                {/* Decorative background shape */}
                <div
                  className="absolute top-0 right-0 w-24 h-24 rounded-bl-full opacity-10"
                  style={{
                    backgroundColor:
                      index === 0
                        ? "#617C58"
                        : index === 1
                        ? "#3b82f6"
                        : "#f59e0b",
                  }}
                ></div>

                <div
                  className="w-16 h-16 flex items-center justify-center rounded-full mb-6 relative z-10"
                  style={{
                    backgroundColor:
                      index === 0
                        ? "#f0f7ee"
                        : index === 1
                        ? "#eef4ff"
                        : "#fff8ee",
                    color:
                      index === 0
                        ? "#617C58"
                        : index === 1
                        ? "#3b82f6"
                        : "#f59e0b",
                  }}
                >
                  {resource.icon}
                </div>

                <h3 className="text-2xl font-bold mb-3">{resource.title}</h3>
                <p className="text-gray-600 mb-6 flex-grow">
                  {resource.description}
                </p>

                <span
                  className="inline-flex items-center font-medium px-5 py-2 rounded-lg"
                  style={{
                    backgroundColor:
                      index === 0
                        ? "#f0f7ee"
                        : index === 1
                        ? "#eef4ff"
                        : "#fff8ee",
                    color:
                      index === 0
                        ? "#617C58"
                        : index === 1
                        ? "#3b82f6"
                        : "#f59e0b",
                  }}
                >
                  {resource.action}
                  <svg
                    className="w-4 h-4 ml-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M14 5l7 7m0 0l-7 7m7-7H3"
                    ></path>
                  </svg>
                </span>
              </motion.a>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 px-6 relative overflow-hidden">
        {/* Background image with overlay */}
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-black/60 z-10"></div>
          <img
            src="/assets/help-bg.jpg"
            alt="Support Background"
            className="w-full h-full object-cover object-center"
          />
        </div>

        <div className="max-w-5xl mx-auto relative z-20">
          <div className="bg-white/10 backdrop-blur-sm p-12 rounded-2xl border border-white/20 shadow-xl">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.7 }}
                viewport={{ once: true, margin: "-100px" }}
              >
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                  Still need help?
                </h2>
                <div className="w-20 h-1 bg-white mb-8"></div>
                <p className="text-white/90 text-lg mb-8 leading-relaxed">
                  Our dedicated support team is ready to assist you with any
                  questions or issues you may have. We're committed to providing
                  you with the best possible experience.
                </p>

                <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                  <motion.a
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    href="mailto:<EMAIL>"
                    className="bg-white text-gray-900 font-medium px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors shadow-lg flex items-center justify-center"
                  >
                    <span>Contact Support</span>
                    <svg
                      className="w-5 h-5 ml-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      ></path>
                    </svg>
                  </motion.a>
                  <motion.a
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    href="#"
                    className="bg-transparent text-white font-medium px-8 py-4 rounded-lg border border-white hover:bg-white/10 transition-colors flex items-center justify-center"
                  >
                    <span>Live Chat</span>
                    <svg
                      className="w-5 h-5 ml-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                      ></path>
                    </svg>
                  </motion.a>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.7, delay: 0.2 }}
                viewport={{ once: true, margin: "-100px" }}
                className="bg-white/10 backdrop-blur-sm p-8 rounded-xl border border-white/20"
              >
                <h3 className="text-2xl font-bold text-white mb-6">
                  Support Hours
                </h3>
                <ul className="space-y-4 text-white/90">
                  <li className="flex justify-between items-center pb-2 border-b border-white/10">
                    <span>Monday - Friday</span>
                    <span className="font-medium">9:00 AM - 8:00 PM</span>
                  </li>
                  <li className="flex justify-between items-center pb-2 border-b border-white/10">
                    <span>Saturday</span>
                    <span className="font-medium">10:00 AM - 6:00 PM</span>
                  </li>
                  <li className="flex justify-between items-center pb-2 border-b border-white/10">
                    <span>Sunday</span>
                    <span className="font-medium">Closed</span>
                  </li>
                </ul>

                <div className="mt-8 pt-4 border-t border-white/10">
                  <h4 className="font-bold text-white mb-2">
                    Emergency Support
                  </h4>
                  <p className="text-white/80 mb-4">
                    For urgent issues outside of business hours:
                  </p>
                  <a
                    href="tel:+1234567890"
                    className="text-white font-medium flex items-center"
                  >
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                      ></path>
                    </svg>
                    +1 (234) 567-890
                  </a>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default HelpCenter;
