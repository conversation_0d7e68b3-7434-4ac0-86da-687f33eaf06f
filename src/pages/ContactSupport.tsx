import React from "react";

const ContactSupport: React.FC = () => {
  const supportOptions = [
    {
      title: "Live Chat",
      desc: "Talk to us instantly—24/7 support, right here.",
      action: "Start Chat",
      link: "#",
    },
    {
      title: "Email Us",
      desc: "Drop us a <NAME_EMAIL>—we’ll get back fast.",
      action: "Send Email",
      link: "mailto:<EMAIL>",
    },
    {
      title: "Call Us",
      desc: "Ring 1-800-HazelsOne anytime—real people, real help.",
      action: "Call Now",
      link: "tel:**************",
    },
  ];

  return (
    <main className="bg-white text-black py-12 px-6 font-['SF_Pro_Display','Arial',sans-serif] leading-relaxed antialiased">
      {/* Hero Section */}
      <section className="max-w-7xl mx-auto text-center mb-20">
        <h1 className="text-5xl md:text-6xl font-bold mb-4 tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-black to-[#617C58]">
          Contact Support
        </h1>
        <p className="text-xl md:text-2xl text-gray-600 max-w-2xl mx-auto leading-snug">
          We’re here to help—anytime, anywhere you need assistance.
        </p>
      </section>

      {/* Support Options Section */}
      <section className="max-w-5xl mx-auto mb-20">
        <h2 className="text-3xl md:text-4xl text-black font-semibold text-center mb-10 tracking-tight">
          Get in Touch
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {supportOptions.map((option, index) => (
            <div
              key={index}
              className="p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl hover:-translate-y-2 transition-transform duration-300 text-center border border-black/10"
            >
              <h3 className="text-xl font-semibold text-[#617C58] mb-3">
                {option.title}
              </h3>
              <p className="text-base text-gray-700 mb-6">{option.desc}</p>
              <a
                href={option.link}
                className="inline-block px-6 py-3 bg-black text-white font-medium rounded-full shadow-md transition-colors duration-300 hover:bg-[#617C58] hover:shadow-lg"
              >
                {option.action}
              </a>
            </div>
          ))}
        </div>
      </section>

      {/* Message Form Section */}
      <section className="max-w-5xl mx-auto bg-gradient-to-br from-gray-50 to-gray-100 p-10 rounded-3xl shadow-md">
        <h2 className="text-3xl md:text-4xl text-black font-semibold text-center mb-10 tracking-tight">
          Send Us a Message
        </h2>
        <form className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-black mb-2">
              Name
            </label>
            <input
              type="text"
              className="w-full p-4 bg-white border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
              placeholder="Your Name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-black mb-2">
              Email
            </label>
            <input
              type="email"
              className="w-full p-4 bg-white border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
              placeholder="Your Email"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-black mb-2">
              Message
            </label>
            <textarea
              className="w-full p-4 bg-white border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
              rows={5}
              placeholder="How can we assist you?"
            />
          </div>
          <button
            type="submit"
            className="w-full px-8 py-4 bg-black text-white font-semibold rounded-full shadow-lg transition-colors duration-300 hover:bg-[#617C58] hover:shadow-xl"
          >
            Submit
          </button>
        </form>
      </section>
    </main>
  );
};

export default ContactSupport;
