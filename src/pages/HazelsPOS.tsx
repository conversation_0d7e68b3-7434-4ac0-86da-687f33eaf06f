import React from "react";
import { useNavigate } from "react-router-dom";

const HazelPOS: React.FC = () => {
  const navigate = useNavigate();

  const handleNavigate = (path: string) => {
    navigate(path);
    window.scrollTo(0, 0);
  };

  const features = [
    {
      title: "Complete Business Management",
      desc: "Run everything from a single, intuitive dashboard—sales, inventory, and schedules, all in sync.",
      icon: (
        <svg
          className="w-12 h-12 text-white"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
          <line x1="3" y1="9" x2="21" y2="9" />
          <line x1="9" y1="21" x2="9" y2="9" />
        </svg>
      ),
      img: "https://via.placeholder.com/600x400.png?text=Feature+1",
    },
    {
      title: "Real-Time Analytics",
      desc: "Get instant insights—sales trends and customer behavior to fuel smarter growth.",
      icon: (
        <svg
          className="w-12 h-12 text-white"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M3 17v4h4M7 17l7-7M10 14l7-7M17 7v4h4" />
          <polyline points="3 17 7 13 10 16 17 9" />
        </svg>
      ),
      img: "https://via.placeholder.com/600x400.png?text=Feature+2",
    },
    {
      title: "Customizable & Scalable",
      desc: "Tailor it to your needs—scales effortlessly from one store to a full chain.",
      icon: (
        <svg
          className="w-12 h-12 text-white"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M12 2v6M12 10v6M2 12h6M10 12h6M18 12h4" />
        </svg>
      ),
      img: "https://via.placeholder.com/600x400.png?text=Feature+3",
    },
    {
      title: "Enhanced Customer Experience",
      desc: "Build loyalty with ease—integrated perks and digital receipts that impress.",
      icon: (
        <svg
          className="w-12 h-12 text-white"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
          <circle cx="12" cy="7" r="4" />
        </svg>
      ),
      img: "https://via.placeholder.com/600x400.png?text=Feature+4",
    },
  ];

  const advantages = [
    "Track sales via the HazelCLOUD directly on your mobile or computer",
    "Customized features such as bill sorting and inventory management",
    "Reduce manual work with integrated accounting systems and personnel records",
    "Invoice directly from the cash register with the option for collective invoicing",
    "Save receipt paper with digital trash",
  ];

  return (
    <main className="font-['SF_Pro_Display','Arial',sans-serif] text-gray-900 leading-relaxed antialiased">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-b from-gray-900 to-black pt-24 pb-32 px-6 overflow-hidden">
        <div className="absolute inset-0 bg-[url('/assets/hazelposbg1.jpg')] bg-cover bg-center opacity-30 pointer-events-none"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 to-transparent"></div>

        <div className="max-w-7xl mx-auto relative z-10 flex flex-col items-center text-center">
          <h1 className="text-5xl md:text-7xl font-extrabold tracking-tight text-white animate-fade-in-down">
            HazelPOS
          </h1>
          <p className="text-xl md:text-2xl text-gray-200 mt-4 mb-10 max-w-3xl leading-snug animate-fade-in-up">
            A seamless, all-in-one POS solution for every industry.
          </p>
          <div className="w-full max-w-5xl mx-auto rounded-2xl overflow-hidden shadow-2xl mb-12 transform hover:scale-105 transition-transform duration-500">
            <img
              src="/assets/hazelposbg.jpg"
              alt="HazelPOS System"
              className="w-full h-auto object-cover"
            />
          </div>
          <button
            onClick={() => handleNavigate("/joinus")}
            className="bg-[#617C58] hover:bg-[#8A8AD6] text-white py-4 px-8 rounded-full font-semibold text-lg shadow-lg transform hover:scale-105 transition-all duration-300 animate-fade-in-up"
          >
            Discover HazelPOS
          </button>
        </div>
      </section>

      {/* One System for All Industries Section */}
      <section className="bg-white py-24 px-6">
        <div className="max-w-7xl mx-auto flex flex-col md:flex-row items-center gap-12">
          <div className="md:w-1/2">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 tracking-tight text-gray-900 animate-fade-in-down">
              One System for All Industries
            </h2>
            <p className="text-lg text-gray-600 mb-6 leading-relaxed animate-fade-in-up">
              HazelPOS is a user-friendly, custom-developed cash register system
              designed to simplify your operations. Whether you run a
              restaurant, grocery store, retail shop, or café, HazelPOS adapts
              to your needs with smart features, tailored functions, and
              accessories for businesses of all sizes. Featuring an intuitive
              touchscreen and a crystal-clear customer display, it’s the
              ultimate solution for modern businesses.
            </p>
            <p className="text-lg text-gray-600 mb-8 leading-relaxed animate-fade-in-up">
              Ready to work smarter with HazelPOS?
            </p>
            <button
              onClick={() => handleNavigate("/joinus")}
              className="bg-[#617C58] hover:bg-[#8A8AD6] text-white py-3 px-8 rounded-full font-semibold text-lg shadow-lg transform hover:scale-105 transition-all duration-300 animate-fade-in-up"
            >
              Book a Demo
            </button>
          </div>
          <div className="md:w-1/2">
            <img
              src="/assets/hazelposimg1.jpg"
              alt="HazelPOS System"
              className="w-full h-auto object-cover rounded-2xl shadow-xl transform hover:scale-105 transition-transform duration-500"
            />
          </div>
        </div>
      </section>

      {/* Advantages of HazelPOS Section */}
      <section className="bg-white py-24 px-6">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold mb-8 text-center tracking-tight text-gray-900 animate-fade-in-down">
            Advantages of HazelPOS
          </h2>
          <p className="text-lg text-gray-600 mb-12 text-center max-w-3xl mx-auto leading-relaxed animate-fade-in-up">
            HazelPOS delivers a secure, end-to-end solution that streamlines and
            simplifies your sales process. Here’s how it empowers your business:
          </p>
          <ul className="space-y-6 max-w-2xl mx-auto">
            {advantages.map((advantage, index) => (
              <li
                key={index}
                className="flex items-start animate-fade-in-up"
                style={{ animationDelay: `${index * 200}ms` }}
              >
                <span className="text-[#617C58] mr-4 text-2xl">✔</span>
                <span className="text-lg text-gray-600">{advantage}</span>
              </li>
            ))}
          </ul>
        </div>
      </section>

      {/* Smart Customer Display Section */}
      <section className="bg-white py-24 px-6">
        <div className="max-w-7xl mx-auto flex flex-col md:flex-row-reverse items-center gap-12">
          <div className="md:w-1/2">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 tracking-tight text-gray-900 animate-fade-in-down">
              Smart Customer Display
            </h2>
            <p className="text-lg text-gray-600 mb-6 leading-relaxed animate-fade-in-up">
              Transform the checkout experience with HazelPOS’s smart customer
              display. Showcase slideshows, videos, and more! Integrated with
              Swish Handel, it generates a unique QR code for easy scanning,
              with payment confirmation seamlessly entered into the checkout
              upon completion.
            </p>
            <h2 className="text-xl md:text-xl font-bold mb-6 tracking-tight text-gray-900 animate-fade-in-down">
              Support & Warranty
            </h2>
            <p className="text-lg text-gray-600 mb-6 leading-relaxed animate-fade-in-up">
              With our rental agreement, HazelPOS offers comprehensive support
              and assistance throughout your contract. Enjoy a full warranty,
              regular software updates, new features, and reliable backups,
              ensuring protection against unexpected costs. We’re committed to
              long-term customer satisfaction, keeping you 100% supported.
            </p>
            <button
              onClick={() => handleNavigate("/joinus")}
              className="bg-[#617C58] hover:bg-[#8A8AD6] text-white py-3 px-8 rounded-full font-semibold text-lg shadow-lg transform hover:scale-105 transition-all duration-300 animate-fade-in-up"
            >
              Book a Demo
            </button>
          </div>

          <div className="md:w-1/2">
            <img
              src="/assets/hazelposimg2.jpg"
              alt="Smart Customer Display"
              className="w-full h-auto object-cover rounded-2xl shadow-xl transform hover:scale-105 transition-transform duration-500"
            />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="bg-white py-24 px-6">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold text-center mb-16 tracking-tight text-gray-900 animate-fade-in-down">
            What Makes HazelPOS Shine
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-10">
            {features.map((feature, index) => (
              <div
                key={index}
                className="relative p-8 bg-white rounded-3xl shadow-lg hover:shadow-xl hover:-translate-y-2 transition-all duration-500 animate-fade-in-up"
                style={{ animationDelay: `${index * 200}ms` }}
              >
                <div className="w-16 h-16 bg-gradient-to-br from-[#617C58] to-[#8A8AD6] rounded-full flex items-center justify-center mb-6 mx-auto">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-4 text-center text-gray-900">
                  {feature.title}
                </h3>
                <p className="text-base text-gray-600 text-center">
                  {feature.desc}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="relative bg-gradient-to-b from-gray-900 to-black py-32 px-6 text-white overflow-hidden">
        <div className="absolute inset-0 bg-[url('https://via.placeholder.com/1200x600.png?text=CTA+Background')] bg-cover bg-center opacity-20 pointer-events-none"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-black/80 to-gray-900/50"></div>

        <div className="relative max-w-7xl mx-auto flex flex-col items-center text-center">
          <h2 className="text-5xl md:text-6xl font-extrabold mb-6 tracking-tight animate-fade-in-down">
            Ready to Supercharge Your Business?
          </h2>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto mb-10 leading-relaxed text-gray-200 animate-fade-in-up">
            Discover how HazelPOS can streamline your operations, boost
            efficiency, and grow your bottom line with a unified POS system.
          </p>
          <button
            onClick={() => handleNavigate("/joinus")}
            className="relative bg-[#617C58] hover:bg-[#8A8AD6] text-white py-4 px-10 rounded-full font-semibold text-lg shadow-lg transform hover:scale-105 transition-all duration-300 ease-in-out animate-fade-in-up"
          >
            <span className="relative z-10">Get Started Today</span>
            <span className="absolute inset-0 rounded-full bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500"></span>
          </button>
          <p className="mt-8 text-base text-gray-400 animate-fade-in-up">
            Need more info?{" "}
            <a
              href="/demo"
              className="underline hover:text-[#617C58] transition-colors duration-300"
            >
              Book a Demo
            </a>
          </p>
        </div>
      </section>
    </main>
  );
};

export default HazelPOS;
