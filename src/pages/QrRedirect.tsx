import { useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import axios from "axios";
import { API_BASE_URL } from "../constants/api";
import { ApiResponse, QRCodeResponse } from "../interface/interface";
import { motion } from "framer-motion";

export default function QrRedirect() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const qrId = searchParams.get("id");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (qrId) {
      const appLink = `hazelpay://qrcode?id=${qrId}`;

      window.location.href = appLink;

      const timer = setTimeout(async () => {
        try {
          setIsLoading(true);
          setError(null);

          const qrResponse = await axios.get<ApiResponse<QRCodeResponse>>(
            `${API_BASE_URL}/online/public/qrcode-web?qrcodeId=${qrId}`
          );

          if (qrResponse.data.status) {
            if (qrResponse.data.data) {
              const qrCodeData = qrResponse.data.data;

              if (qrCodeData.transactionId) {
                const transactionId = qrCodeData.transactionId;
                window.location.href = `${API_BASE_URL}/online/public/checkout?transactionId=${transactionId}`;
              } else {
                setError("Could not retrieve transaction information");
              }
            } else {
              setError(qrResponse.data.message);
            }
          } else {
            setError(
              qrResponse.data.message ||
                "Could not retrieve QR code information"
            );
          }
        } catch (err) {
          setError("An error occurred while processing your request");
          console.error("Error processing QR code:", err);
        } finally {
          setIsLoading(false);
        }
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [qrId]);

  const getStatusMessage = () => {
    if (isLoading) {
      return (
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#617C58] mb-4"></div>
          <p className="text-gray-600">Processing your request...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className="text-center">
          <p className="text-red-500 font-medium mb-6">{error}</p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => navigate("/")}
            className="bg-[#617C58] hover:bg-[#4e6346] text-white py-3 px-6 rounded-md transition-colors duration-300 font-medium"
          >
            Return to Homepage
          </motion.button>
        </div>
      );
    }

    return (
      <p className="text-gray-600">
        If the app doesn't open, we'll process your request in the browser.
      </p>
    );
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-white to-gray-50 text-center">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md px-6 py-10 bg-white rounded-lg shadow-md"
      >
        {!error && (
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            Opening HazelPay...
          </h1>
        )}
        {error && (
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            QR Code Status
          </h1>
        )}
        {getStatusMessage()}
      </motion.div>
    </div>
  );
}
