import React from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";

const NotFound: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
      <div className="max-w-2xl w-full bg-white rounded-lg shadow-xl overflow-hidden">
        <div className="flex flex-col md:flex-row">
          {/* Left side with illustration */}
          <div className="bg-[#617C58] text-white p-8 md:p-12 flex flex-col justify-center items-center md:w-1/2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-center"
            >
              <h1 className="text-9xl font-bold">404</h1>
              <div className="mt-4 text-xl font-light">Page not found</div>
            </motion.div>
          </div>

          {/* Right side with content */}
          <div className="p-8 md:p-12 md:w-1/2">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <h2 className="text-2xl font-bold text-gray-800 mb-4">
                Oops! We can't find that page.
              </h2>
              <p className="text-gray-600 mb-8">
                The page you're looking for might have been moved, deleted, or
                possibly never existed. Let's get you back on track.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  onClick={() => navigate("/")}
                  className="bg-[#617C58] hover:bg-[#4e6346] text-white py-2 px-6 rounded-md transition-colors duration-300 font-medium"
                >
                  Go to Homepage
                </button>
              </div>

              <div className="mt-8 text-sm text-gray-500">
                <p>
                  Need help? Contact our{" "}
                  <button
                    onClick={() => navigate("/help")}
                    className="text-[#617C58] hover:underline"
                  >
                    support team
                  </button>
                  .
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
