import React, { useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import {
  HiCheckCircle,
  HiXCircle,
  HiHome,
  HiQuestionMarkCircle,
} from "react-icons/hi";

const TransactionReturn: React.FC = () => {
  const [searchParams] = useSearchParams();
  const status = searchParams.get("status");
  const navigate = useNavigate();

  const isValidStatus = status === "success" || status === "failed";

  useEffect(() => {
    if (!isValidStatus) {
      const timer = setTimeout(() => {
        navigate("/");
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [isValidStatus, navigate]);

  const getContent = () => {
    if (!isValidStatus) {
      return {
        icon: <HiQuestionMarkCircle className="w-20 h-20 text-yellow-500" />,
        title: "Invalid Status",
        message:
          "The transaction status is invalid. Redirecting to home page...",
        bgColor: "bg-yellow-50",
        borderColor: "border-yellow-200",
        textColor: "text-yellow-700",
      };
    }

    if (status === "success") {
      return {
        icon: <HiCheckCircle className="w-20 h-20 text-[#617C58]" />,
        title: "Transaction Successful",
        message:
          "Your transaction has been processed successfully. You can check the details in the HazelsOne app.",
        bgColor: "bg-green-50",
        borderColor: "border-green-200",
        textColor: "text-green-700",
      };
    } else {
      return {
        icon: <HiXCircle className="w-20 h-20 text-red-500" />,
        title: "Transaction Failed",
        message:
          "We couldn't process your transaction. This could be due to insufficient funds, network issues, or other payment problems.",
        bgColor: "bg-red-50",
        borderColor: "border-red-200",
        textColor: "text-red-700",
      };
    }
  };

  const content = getContent();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-2xl w-full bg-white rounded-lg shadow-xl overflow-hidden"
      >
        <div className="flex flex-col">
          <div
            className={`${content.bgColor} ${content.borderColor} border-b p-8 flex flex-col items-center justify-center text-center`}
          >
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              {content.icon}
            </motion.div>
            <motion.h1
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="text-3xl font-bold mt-4 text-gray-800"
            >
              {content.title}
            </motion.h1>
          </div>

          <div className="p-8">
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="text-lg text-gray-600 mb-8 text-center"
            >
              {content.message}
            </motion.p>

            {status === "success" && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-8"
              >
                <h3 className="font-medium text-gray-800 mb-2">What's Next?</h3>
                <ul className="list-disc pl-5 space-y-2 text-gray-600">
                  <li>Check your transaction details in the HazelsOne app</li>
                  <li>A receipt has been sent to your registered email</li>
                  <li>For any questions, contact our support team</li>
                </ul>
              </motion.div>
            )}

            {status === "failed" && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-8"
              >
                <h3 className="font-medium text-gray-800 mb-2">
                  Possible Reasons:
                </h3>
                <ul className="list-disc pl-5 space-y-2 text-gray-600">
                  <li>Insufficient funds in your account</li>
                  <li>Card expired or invalid card details</li>
                  <li>Network or connectivity issues</li>
                  <li>Transaction declined by your bank</li>
                </ul>
              </motion.div>
            )}

            <div className="flex justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate("/")}
                className="flex items-center bg-[#617C58] hover:bg-[#4e6346] text-white py-3 px-6 rounded-md transition-colors duration-300 font-medium"
              >
                <HiHome className="mr-2" />
                Return to Homepage
              </motion.button>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default TransactionReturn;
