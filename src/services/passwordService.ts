import axios from "axios";
import { ApiResponse } from "../interface/interface";
import { API_ENDPOINTS } from "../constants/api";

export interface ResetPasswordData {
  token: string;
  password: string;
}

export const resetPassword = async (
  data: ResetPasswordData
): Promise<ApiResponse<null>> => {
  try {
    const response = await axios.post<ApiResponse<null>>(
      API_ENDPOINTS.RESET_PASSWORD,
      data
    );
    return response.data;
  } catch (error: any) {
    throw error.response?.data?.message || "Failed to reset password.";
  }
};
