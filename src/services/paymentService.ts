import axios from "axios";
import {
  ApiR<PERSON>ponse,
  CreateStaticQRPaymentNewData,
  StaticQRDetailsResponse,
} from "../interface/interface";
import { API_ENDPOINTS } from "../constants/api";

export const fetchStaticQRDetails = async (
  staticQRId: string
): Promise<ApiResponse<StaticQRDetailsResponse>> => {
  try {
    const response = await axios.get<ApiResponse<StaticQRDetailsResponse>>(
      `${API_ENDPOINTS.STATIC_QR_DETAILS}?staticQRId=${staticQRId}`
    );
    return response.data;
  } catch (error: any) {
    throw error.response?.data?.message || "Failed to fetch static QR details.";
  }
};

export const getCheckoutUrl = (transactionId: string): string => {
  return `${API_ENDPOINTS.CHECKOUT}?transactionId=${transactionId}`;
};

export const createStaticQRPaymentNew = async (
  data: CreateStaticQRPaymentNewData
): Promise<ApiResponse<string>> => {
  try {
    const response = await axios.post<ApiResponse<string>>(
      API_ENDPOINTS.CREATE_STATIC_QR_PAYMENT_NEW,
      data
    );
    return response.data;
  } catch (error: any) {
    throw error.response?.data?.message || "Failed to create payment.";
  }
};
