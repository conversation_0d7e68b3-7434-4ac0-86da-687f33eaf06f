import axios from "axios";
import { ApiResponse } from "../interface/interface";
import { API_ENDPOINTS } from "../constants/api";

export interface OnboardingRequestData {
  email: string;
  merchantType: string;
  country: string;
  companyName: string;
  phone?: string;
  expectedTransactionsPerMonth: string;
  expectedMonthlyActiveTerminals: string;
}

export const submitOnboardingRequest = async (
  data: OnboardingRequestData
): Promise<ApiResponse<null>> => {
  try {
    const response = await axios.post<ApiResponse<null>>(
      API_ENDPOINTS.ONBOARDING,
      data
    );
    return response.data;
  } catch (error: any) {
    throw (
      error.response?.data?.message || "Failed to submit onboarding request."
    );
  }
};
