import "./App.css";
import Footer from "./components/Footer";
import Header from "./components/Header";
import { motion } from "framer-motion";
import { Route, Routes } from "react-router-dom";
import Home from "./pages/Home";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import TermsOfUse from "./pages/TermsOfUse";
import ComingSoon from "./components/ComingSoon";
import HelpCenter from "./pages/HelpCenter";
import BasicSignup from "./pages/BasicSignup";
import HazelPOS from "./pages/HazelsPOS";
import HazelPay from "./pages/HazelsPay";
import QrRedirect from "./pages/QrRedirect";
import ForgetPassword from "./pages/ForgetPassword";
import NotFound from "./pages/NotFound";
import Company from "./pages/Company";
import TransactionReturn from "./pages/TransactionReturn";
import StaticQrPayment from "./pages/StaticQrPayment";

function App() {
  return (
    <>
      <Header />
      <motion.div
        initial={{ opacity: 0, y: 0 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/privacypolicy" element={<PrivacyPolicy />} />
          <Route path="/termsofuse" element={<TermsOfUse />} />
          <Route path="/hazelpay" element={<HazelPay />} />
          <Route path="/hazelpos" element={<HazelPOS />} />
          <Route path="/company" element={<Company />} />
          <Route path="/team" element={<ComingSoon />} />
          <Route path="/mission" element={<ComingSoon />} />
          <Route path="/help" element={<HelpCenter />} />
          <Route path="/joinus" element={<BasicSignup />} />
          {/* Preprod Routes */}
          <Route path="/preprod/qrcode" element={<QrRedirect />} />
          <Route
            path="/preprod/forget-password/:hash"
            element={<ForgetPassword />}
          />
          <Route path="/preprod/pay/static" element={<StaticQrPayment />} />

          {/* Prod Routes */}
          <Route path="/prod/qrcode" element={<QrRedirect />} />
          <Route
            path="/prod/forget-password/:hash"
            element={<ForgetPassword />}
          />
          <Route path="/prod/pay/static" element={<StaticQrPayment />} />

          <Route path="/return" element={<TransactionReturn />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </motion.div>
      <Footer />
    </>
  );
}

export default App;
