const isProd = (): boolean => {
  if (typeof window !== "undefined") {
    const currentUrl = window.location.href;

    if (currentUrl.includes("/prod/")) {
      return true;
    }

    if (currentUrl.includes("/preprod/")) {
      return false;
    }
  }

  // Default to preprod if we can't determine
  return false;
};

// API URLs based on environment
export const API_BASE_URL = isProd()
  ? import.meta.env.VITE_API_PROD_URL || "http://localhost:8081"
  : import.meta.env.VITE_API_PREPROD_URL || "http://localhost:8081";

export const BACKEND_API_BASE_URL = isProd()
  ? import.meta.env.VITE_HAZELSONE_API_PROD_URL || "http://localhost:3000"
  : import.meta.env.VITE_HAZELSONE_API_PREPROD_URL || "http://localhost:3000";

const API_PREFIX = API_BASE_URL.includes("localhost")
  ? "/public"
  : "/api/public";

export const API_ENDPOINTS = {
  ONBOARDING: `${API_BASE_URL}${API_PREFIX}/onboarding-request`,
  RESET_PASSWORD: `${BACKEND_API_BASE_URL}/password-reset/reset`,
  STATIC_QR_DETAILS: `${API_BASE_URL}/online/public/static-qr-details`,
  CREATE_STATIC_QR_PAYMENT_NEW: `${BACKEND_API_BASE_URL}/qrcodes/public/create-static-qr-payment`,
  CHECKOUT: `${API_BASE_URL}/online/public/checkout`,
};
