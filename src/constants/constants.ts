import { Story } from "../interface/interface";

export const countries = [
  "Austria",
  "Belgium",
  "Bulgaria",
  "Croatia",
  "Cyprus",
  "Czechia",
  "Denmark",
  "Estonia",
  "Finland",
  "France",
  "Germany",
  "Greece",
  "Hungary",
  "Ireland",
  "Italy",
  "Latvia",
  "Lithuania",
  "Luxembourg",
  "Malta",
  "Netherlands",
  "Poland",
  "Portugal",
  "Romania",
  "Slovakia",
  "Slovenia",
  "Spain",
  "Sweden",
  "United Kingdom",
];
export const clientStories: Story[] = [
  {
    id: 1,
    text: "As a small business owner, HazelsOne’s integrated solutions have drastically cut down wait times and boosted customer satisfaction. My staff loves how easy it is to use, and I’m thrilled with the real-time insights into our sales data.",
    name: "<PERSON>",
    avatar: "/assets/hazelsone1.jpg",
  },
  {
    id: 2,
    text: "I never realized how much time I was wasting on manual transaction tracking until I switched to HazelsOne. Now I can see all my sales, inventory, and customer data in one place, and it’s been a game-changer for my bottom line.",
    name: "<PERSON>",
    avatar: "/assets/hazelsone2.jpg",
  },
  {
    id: 3,
    text: "The HazelsOne platform is incredibly user-friendly, and their support team has been amazing. We set up HazelPay terminals in all our branches, and everything runs smoothly. I’d recommend them to anyone looking to streamline their payment processes.",
    name: "<PERSON> Shaifer",
    avatar: "/assets/hazelsone3.jpg",
  },
];
