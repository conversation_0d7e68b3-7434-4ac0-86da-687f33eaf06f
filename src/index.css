@import "tailwindcss";

body {
  font-family: "Inter Tight", sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f7f9fc;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.btn {
  @apply bg-blue-600 text-white px-6 py-3 rounded transition duration-300 hover:bg-blue-700;
}

.gradient-bg {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
}

.container {
  @apply max-w-6xl mx-auto px-4;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(-2deg);
  }
}

@keyframes floatSlow {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(2deg);
  }
}

@keyframes pulseSlow {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.animate-float {
  animation: float 4s ease-in-out infinite;
}

.animate-float-slow {
  animation: floatSlow 5s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulseSlow 3s ease-in-out infinite;
}
